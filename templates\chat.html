{% extends "base.html" %}

{% block title %}Chat - Kawaii Chat 🌸{% endblock %}

{% block content %}
<!-- Message Selection Header -->
<div id="selectionHeader" class="selection-header">
    <div class="selection-info">
        <button class="selection-btn" onclick="clearSelection()">
            <i class="fas fa-times"></i>
        </button>
        <span class="selection-count" id="selectionCount">0 selected</span>
    </div>
    <div class="selection-actions">
        <button class="selection-btn" onclick="replyToSelected()" id="replyBtn">
            <i class="fas fa-reply"></i>
            <span>Reply</span>
        </button>
        <button class="selection-btn" onclick="deleteSelected()" id="deleteBtn">
            <i class="fas fa-trash"></i>
            <span>Delete</span>
        </button>
    </div>
</div>
<!-- Navigation Menu -->
<nav class="main-nav">
    <div class="nav-container">
        <div class="nav-brand">
            <i class="fas fa-comments"></i>
            <span>Chat App</span>
        </div>

        <button class="nav-toggle" id="navToggle" onclick="toggleNavMenu()">
            <span></span>
            <span></span>
            <span></span>
        </button>

        <div class="nav-menu" id="navMenu">
            <a href="{{ url_for('chat') }}" class="nav-link active">
                <i class="fas fa-comments"></i>
                <span>Chat</span>
            </a>
            <a href="{{ url_for('profile') }}" class="nav-link">
                <i class="fas fa-user-circle"></i>
                <span>Profile</span>
            </a>
            <button class="nav-link nav-btn" onclick="document.getElementById('addContactModal').style.display='flex'; document.getElementById('addContactModal').classList.add('show'); toggleNavMenu();">
                <i class="fas fa-user-plus"></i>
                <span>Add Contact</span>
            </button>
            <a href="{{ url_for('logout') }}" class="nav-link logout">
                <i class="fas fa-sign-out-alt"></i>
                <span>Logout</span>
            </a>
        </div>
    </div>
</nav>

<div class="chat-container">
    <!-- Sidebar -->
    <aside class="chat-sidebar">
        <!-- User Info -->
        <div class="sidebar-header">
            <div class="user-info">
                <div class="user-avatar" style="background: linear-gradient(45deg, #ff6b9d, #c44569)">
                    {% if session.get('profile_picture_url') %}
                        <img src="{{ session.get('profile_picture_url') }}" alt="Your Profile">
                    {% else %}
                        <i class="fas fa-user"></i>
                    {% endif %}
                </div>
                <div class="user-details">
                    <h3>{{ session.username }}</h3>
                    <span class="user-status" id="myStatus" style="color: #00a884;">Online 🟢</span>
                </div>
            </div>
            <!-- Sidebar actions hidden on mobile, using top nav menu instead -->
        </div>
        
        <!-- Contacts List -->
        <div class="contacts-section">
            <h4 class="section-title">
                <i class="fas fa-users"></i>
                Your Contacts
            </h4>
            <div class="contacts-list" id="contactsList">
                {% if contacts %}
                    {% for contact in contacts %}
                    <div class="contact-item" data-contact-id="{{ contact[0] }}" onclick="openChat('{{ contact[0] }}', '{{ (contact[3] or contact[1])|replace("'", "\\'") }}', '{{ contact[2] }}', '{{ contact[5] if contact|length > 5 and contact[5] else '' }}', '{{ contact[6] if contact|length > 6 and contact[6] else '' }}')">
                        <div class="contact-avatar" style="background: {{ contact[2] }}">
                            {% if contact|length > 5 and contact[5] %}
                                <img src="{{ contact[5] }}" alt="{{ contact[3] or contact[1] }}">
                            {% else %}
                                <i class="fas fa-user"></i>
                            {% endif %}
                        </div>
                        <div class="contact-info">
                            <div class="contact-name">{{ contact[3] or contact[1] }}</div>
                            <div class="contact-status">{{ contact[1] }}</div>
                        </div>
                        <div class="contact-indicator" id="indicator-{{ contact[0] }}">
                            <i class="fas fa-circle" style="color: #ddd;" title="Status: Unknown"></i>
                        </div>
                    </div>
                    {% endfor %}
                {% else %}
                    <div class="no-contacts">
                        <i class="fas fa-heart-broken"></i>
                        <p>No contacts yet!</p>
                        <small>Add friends using their contact codes 💕</small>
                    </div>
                {% endif %}
            </div>
        </div>
    </aside>
    
    <!-- Chat Area -->
    <main class="chat-area">
        <!-- Welcome Screen -->
        <div class="chat-welcome" id="chatWelcome">
            <div class="welcome-content">
                <div class="welcome-mascot">💬</div>
                <h2>Welcome to Chat App!</h2>
                <p>Select a contact to start chatting, or add new friends!</p>

                <!-- Call Feature Info -->
                <div class="call-info-box" style="background: rgba(255, 107, 157, 0.1); padding: 1rem; border-radius: 15px; margin: 1rem 0; border: 2px solid rgba(255, 107, 157, 0.3);">
                    <h3 style="color: #c44569; margin-bottom: 0.5rem;">📞 Voice & Video Calls</h3>
                    <p style="font-size: 0.9rem; margin-bottom: 0.5rem;">Make audio and video calls with your contacts!</p>
                    <p style="font-size: 0.8rem; color: #666; margin-bottom: 1rem;">
                        <strong>First time?</strong> Click "🔐 Allow Microphone" below, then allow access when your browser asks.<br>
                        <strong>No camera?</strong> No problem! Video calls automatically become audio calls.
                    </p>
                    <div style="display: flex; gap: 0.5rem; flex-wrap: wrap; justify-content: center;">
                        <button class="btn-secondary" onclick="runFullDiagnostics()" style="font-size: 0.8rem; padding: 0.5rem 1rem; background: linear-gradient(45deg, #e91e63, #ad1457);">
                            🔍 Full Check
                        </button>
                        <button class="btn-secondary" onclick="requestMediaPermissions(false)" style="font-size: 0.8rem; padding: 0.5rem 1rem;">
                            🔐 Allow Microphone
                        </button>
                        <button class="btn-secondary" onclick="testAudioOnly()" style="font-size: 0.8rem; padding: 0.5rem 1rem;">
                            🎤 Test Audio Only
                        </button>
                        <button class="btn-secondary" onclick="reconnectSocket()" style="font-size: 0.8rem; padding: 0.5rem 1rem;">
                            🔄 Reconnect
                        </button>
                        <button class="btn-secondary" onclick="debugCallSystem()" style="font-size: 0.8rem; padding: 0.5rem 1rem;">
                            🐛 Debug Calls
                        </button>
                        <button class="btn-secondary" onclick="testIncomingCall()" style="font-size: 0.8rem; padding: 0.5rem 1rem;">
                            📞 Test Incoming
                        </button>
                        <button class="btn-secondary" onclick="sendDebugCall()" style="font-size: 0.8rem; padding: 0.5rem 1rem;">
                            🐛 Send Test Call
                        </button>
                        <button class="btn-secondary" onclick="testCallHistory()" style="font-size: 0.8rem; padding: 0.5rem 1rem;">
                            📞 Test Call History
                        </button>
                        <a href="/media_test" target="_blank" class="btn-secondary" style="font-size: 0.8rem; padding: 0.5rem 1rem; text-decoration: none; display: inline-block;">
                            🔧 Advanced
                        </a>
                    </div>
                </div>

                <button class="btn-primary" onclick="document.getElementById('addContactModal').style.display='flex';">
                    <i class="fas fa-user-plus"></i>
                    Add Your First Contact
                </button>
            </div>
        </div>

        <!-- Chat Area -->
        <div class="chat-view" id="chatArea" style="display: none;">
            <!-- Chat Header -->
            <div class="chat-header" id="chatHeader">
                <button class="chat-back-btn mobile-only" id="chatBackBtn" onclick="closeChat()">
                    <i class="fas fa-arrow-left"></i>
                </button>
                <div class="chat-contact-info" onclick="showContactInfo()">
                    <div class="contact-avatar" id="chatAvatar">
                        <i class="fas fa-user"></i>
                    </div>
                    <div class="contact-details">
                        <h3 id="chatContactName">Contact Name</h3>
                        <span class="contact-status" id="contactStatus" style="color: #999;">Last seen recently</span>
                        <div class="connection-status connecting" style="font-size: 0.7rem; margin-top: 2px;">🟡 Connecting...</div>
                    </div>
                </div>
                <div class="chat-actions">
                    <button class="action-btn audio-call" title="Audio Call" onclick="startAudioCall()">
                        <i class="fas fa-phone"></i>
                    </button>
                    <button class="action-btn video-call" title="Video Call" onclick="startVideoCall()">
                        <i class="fas fa-video"></i>
                    </button>
                    <button class="action-btn test-media" title="Test Microphone" onclick="testMicrophoneOnly()">
                        <i class="fas fa-microphone-alt"></i>
                    </button>
                    <button class="action-btn more-options" title="More Options" onclick="showChatOptions()">
                        <i class="fas fa-ellipsis-v"></i>
                    </button>
                </div>
            </div>

            <!-- Messages Container -->
            <div class="messages-container" id="messagesContainer">
                <div class="floating-emojis" id="floatingEmojis"></div>
                <div class="messages-loading" id="messagesLoading" style="display: none;">
                    <div class="loading-spinner"></div>
                    <p>Loading messages...</p>
                </div>
                <!-- Messages will be loaded here -->
            </div>

            <!-- Message Input -->
            <div class="message-input-area" id="messageInputArea">
                <!-- Reply Preview -->
                <div class="reply-preview" id="replyPreview" style="display: none;">
                    <div class="reply-content">
                        <div class="reply-header">
                            <i class="fas fa-reply"></i>
                            <span class="reply-to">Replying to <span id="replyToUser"></span></span>
                            <button class="reply-close" onclick="cancelReply()">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                        <div class="reply-message" id="replyMessage"></div>
                    </div>
                </div>

                <div class="input-container" id="inputContainer">
                    <button class="emoji-btn" onclick="toggleEmojiPicker()" title="Add emoji">
                        😊
                    </button>
                    <input type="text" id="messageInput" placeholder="Type your kawaii message... ✨"
                           maxlength="1000" onkeypress="handleEnterKey(event)" autocomplete="off">
                    <button class="media-btn" onclick="showMediaModal()" title="Send Photo/Video">
                        <i class="fas fa-image"></i>
                    </button>
                    <input type="file" id="imageInput" accept="image/*" style="display: none;" onchange="handleImageUpload(event)">
                    <button class="file-btn" onclick="showFileModal()" title="Send File">
                        <i class="fas fa-paperclip"></i>
                    </button>
                    <button class="send-btn" id="sendBtn" onclick="sendMessage()" title="Send message">
                        <i class="fas fa-paper-plane"></i>
                    </button>
                </div>
                <div class="typing-indicator" id="typingIndicator" style="display: none;">
                    <span class="typing-dots">
                        <span></span>
                        <span></span>
                        <span></span>
                    </span>
                    <span class="typing-text">Someone is typing...</span>
                </div>
            </div>
        </div>
    </main>
</div>

<!-- WhatsApp Style Image Viewer -->
<div id="whatsappImageViewer" class="whatsapp-image-viewer" style="display: none;">
    <div class="image-viewer-header">
        <button class="back-btn" onclick="closeWhatsAppImageViewer()">
            <i class="fas fa-arrow-left"></i>
        </button>
        <div class="image-info">
            <div class="sender-name" id="imageSenderName">Contact</div>
            <div class="image-date" id="imageDate">Today</div>
        </div>
        <div class="image-actions">
            <button class="action-btn" onclick="downloadImage()" title="Download">
                <i class="fas fa-download"></i>
            </button>
            <button class="action-btn" onclick="showImageReactions()" title="React">
                <i class="fas fa-smile"></i>
            </button>
        </div>
    </div>
    <div class="image-viewer-content">
        <img id="viewerImage" src="" alt="Full size image"
             oncontextmenu="showWhatsAppReactions(event, window.currentViewerMessageId); return false;"
             ontouchstart="handleTouchStart(event, window.currentViewerMessageId)"
             ontouchend="handleTouchEnd(event)">
    </div>
    <div class="image-viewer-footer" id="imageViewerReactions">
        <!-- Reactions will be displayed here -->
    </div>
</div>

<!-- WhatsApp Style Reaction Bar -->
<div id="whatsappReactionBar" class="whatsapp-reaction-bar" style="display: none;">
    <div class="reaction-bar-content">
        <span class="reaction-emoji" onclick="addDirectReaction('❤️', this)" title="Love">❤️</span>
        <span class="reaction-emoji" onclick="addDirectReaction('😂', this)" title="Laugh">😂</span>
        <span class="reaction-emoji" onclick="addDirectReaction('😮', this)" title="Wow">😮</span>
        <span class="reaction-emoji" onclick="addDirectReaction('😢', this)" title="Sad">😢</span>
        <span class="reaction-emoji" onclick="addDirectReaction('😡', this)" title="Angry">😡</span>
        <span class="reaction-emoji" onclick="addDirectReaction('👍', this)" title="Like">👍</span>
        <span class="reaction-emoji" onclick="addDirectReaction('👎', this)" title="Dislike">👎</span>
        <div class="reaction-more" onclick="showMoreReactions()" title="More reactions">
            <i class="fas fa-plus"></i>
        </div>
    </div>
</div>

<!-- WhatsApp Style Emoji Picker with Recent -->
<div id="emojiPicker" class="simple-emoji-picker" style="display: none;">
    <div class="emoji-header">
        <span>Choose an emoji</span>
        <button onclick="closeEmojiPicker()" style="background: none; border: none; font-size: 18px; cursor: pointer;">×</button>
    </div>
    <div class="emoji-content" id="emojiContent">
        <!-- Recent Emojis Section -->
        <div class="emoji-section">
            <div class="emoji-section-title">🕒 Recently Used</div>
            <div class="emoji-grid" id="recentEmojiGrid">
                <!-- Recent emojis will be loaded here -->
            </div>
        </div>

        <!-- All Emojis Section -->
        <div class="emoji-section">
            <div class="emoji-section-title">😊 All Emojis</div>
            <div class="emoji-grid" id="allEmojiGrid">
                <!-- All emojis will be loaded here -->
            </div>
        </div>
    </div>
</div>

<!-- More Reactions Modal -->
<div id="moreReactionsModal" class="more-reactions-modal" style="display: none;">
    <div class="more-reactions-content">
        <div class="reactions-header">
            <h3>Choose a reaction</h3>
            <button class="close-reactions" onclick="closeMoreReactions()">×</button>
        </div>
        <div class="reactions-grid">
            <span class="reaction-option" onclick="addDirectReaction('❤️', this)">❤️</span>
            <span class="reaction-option" onclick="addDirectReaction('😂', this)">😂</span>
            <span class="reaction-option" onclick="addDirectReaction('😮', this)">😮</span>
            <span class="reaction-option" onclick="addDirectReaction('😢', this)">😢</span>
            <span class="reaction-option" onclick="addDirectReaction('😡', this)">😡</span>
            <span class="reaction-option" onclick="addDirectReaction('👍', this)">👍</span>
            <span class="reaction-option" onclick="addDirectReaction('👎', this)">👎</span>
            <span class="reaction-option" onclick="addDirectReaction('😍', this)">😍</span>
            <span class="reaction-option" onclick="addDirectReaction('🥰', this)">🥰</span>
            <span class="reaction-option" onclick="addDirectReaction('😘', this)">😘</span>
            <span class="reaction-option" onclick="addDirectReaction('💕', this)">💕</span>
            <span class="reaction-option" onclick="addDirectReaction('💖', this)">💖</span>
            <span class="reaction-option" onclick="addDirectReaction('👏', this)">👏</span>
            <span class="reaction-option" onclick="addDirectReaction('🔥', this)">🔥</span>
            <span class="reaction-option" onclick="addDirectReaction('✨', this)">✨</span>
            <span class="reaction-option" onclick="addDirectReaction('🌟', this)">🌟</span>
            <span class="reaction-option" onclick="addDirectReaction('💯', this)">💯</span>
            <span class="reaction-option" onclick="addDirectReaction('🎉', this)">🎉</span>
        </div>
    </div>
</div>


<!-- Add Contact Modal -->
<div class="modal" id="addContactModal">
    <div class="modal-content">
        <div class="modal-header">
            <h3>
                <i class="fas fa-user-plus"></i>
                Add New Contact
            </h3>
            <button class="close-btn" onclick="document.getElementById('addContactModal').style.display='none'; document.getElementById('addContactModal').classList.remove('show');">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div class="modal-body">
            <form id="addContactForm">
                <div class="form-group">
                    <label for="contactCode">
                        <i class="fas fa-qrcode"></i>
                        Contact Code
                    </label>
                    <input type="text" id="contactCode" placeholder="Enter 8-character code... 🔤" 
                           maxlength="8" style="text-transform: uppercase;">
                </div>
                <div class="form-group">
                    <label for="contactName">
                        <i class="fas fa-tag"></i>
                        Display Name (Optional)
                    </label>
                    <input type="text" id="contactName" placeholder="How should we call them? 💕">
                </div>
            </form>
        </div>
        <div class="modal-footer">
            <button class="btn-secondary" onclick="document.getElementById('addContactModal').style.display='none'; document.getElementById('addContactModal').classList.remove('show');">Cancel</button>
            <button class="btn-primary" onclick="addContactSimple()">
                <i class="fas fa-plus"></i>
                Add Contact
            </button>
        </div>
    </div>
</div>

<!-- Media Upload Modal -->
<div class="modal" id="mediaModal">
    <div class="modal-content">
        <div class="modal-header">
            <h3>
                <i class="fas fa-image"></i>
                Send Photo/Video
            </h3>
            <button class="close-btn" onclick="closeMediaModal()">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div class="modal-body">
            <div class="upload-area" id="mediaUploadArea">
                <div class="upload-zone" onclick="document.getElementById('mediaFileInput').click()">
                    <i class="fas fa-cloud-upload-alt"></i>
                    <p>Click to select photos or videos</p>
                    <small>Supports JPG, PNG, GIF, MP4, MOV (Max 50MB)</small>
                </div>
                <input type="file" id="mediaFileInput" accept="image/*,video/*" style="display: none;" onchange="handleMediaSelect(event)">
            </div>
            <div class="media-preview" id="mediaPreview" style="display: none;">
                <div class="preview-container" id="previewContainer"></div>
                <div class="caption-input">
                    <input type="text" id="mediaCaption" placeholder="Add a caption... 💕" maxlength="500">
                </div>
            </div>
        </div>
        <div class="modal-footer">
            <button class="btn-secondary" onclick="closeMediaModal()">Cancel</button>
            <button class="btn-primary" id="sendMediaBtn" onclick="sendMedia()" disabled>
                <i class="fas fa-paper-plane"></i>
                Send
            </button>
        </div>
    </div>
</div>

<!-- File Upload Modal -->
<div class="modal" id="fileModal">
    <div class="modal-content">
        <div class="modal-header">
            <h3>
                <i class="fas fa-paperclip"></i>
                Send File
            </h3>
            <button class="close-btn" onclick="closeFileModal()">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div class="modal-body">
            <div class="upload-area" id="fileUploadArea">
                <div class="upload-zone" onclick="document.getElementById('fileInput').click()">
                    <i class="fas fa-file-upload"></i>
                    <p>Click to select any file</p>
                    <small>Documents, Audio, Archives (Max 50MB)</small>
                </div>
                <input type="file" id="fileInput" style="display: none;" onchange="handleFileSelect(event)">
            </div>
            <div class="file-preview" id="filePreview" style="display: none;">
                <div class="file-info" id="fileInfo"></div>
                <div class="caption-input">
                    <input type="text" id="fileCaption" placeholder="Add a description... 📎" maxlength="500">
                </div>
            </div>
        </div>
        <div class="modal-footer">
            <button class="btn-secondary" onclick="closeFileModal()">Cancel</button>
            <button class="btn-primary" id="sendFileBtn" onclick="sendFile()" disabled>
                <i class="fas fa-paper-plane"></i>
                Send
            </button>
        </div>
    </div>
</div>

<!-- Video Player Modal -->
<div id="videoPlayerModal" class="video-player-modal" style="display: none;">
    <div class="video-player-overlay" onclick="closeVideoPlayer()"></div>
    <div class="video-player-container">
        <div class="video-player-header">
            <div class="video-player-info">
                <h3 id="videoPlayerTitle">Video</h3>
                <p id="videoPlayerMeta">Sent by User</p>
            </div>
            <button class="video-player-close" onclick="closeVideoPlayer()">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div class="video-player-content">
            <video id="videoPlayerElement" controls preload="metadata" onclick="toggleVideoPlayPause()"
                   onerror="handleVideoError()" onloadstart="handleVideoLoadStart()" oncanplay="handleVideoCanPlay()">
                <source id="videoPlayerSource" src="" type="">
                Your browser does not support the video tag.
            </video>
            <div id="videoErrorMessage" class="video-error-message" style="display: none;">
                <div class="error-icon">
                    <i class="fas fa-exclamation-triangle"></i>
                </div>
                <div class="error-text">
                    <h4>Unable to play video</h4>
                    <p>This video format may not be supported by your browser.</p>
                    <button onclick="downloadCurrentVideo()" class="error-download-btn">
                        <i class="fas fa-download"></i> Download Video
                    </button>
                </div>
            </div>
            <div id="videoLoadingMessage" class="video-loading-message" style="display: none;">
                <div class="loading-spinner">
                    <i class="fas fa-spinner fa-spin"></i>
                </div>
                <div class="loading-text">Loading video...</div>
            </div>
        </div>
        <div class="video-player-controls">
            <button class="video-control-btn" onclick="downloadCurrentVideo()" title="Download Video">
                <i class="fas fa-download"></i>
                Download
            </button>
            <button class="video-control-btn" onclick="toggleVideoFullscreen()" title="Fullscreen">
                <i class="fas fa-expand"></i>
                Fullscreen
            </button>
        </div>
    </div>
</div>

<!-- Incoming Call Modal -->
<div class="call-modal" id="incomingCallModal" style="display: none;">
    <div class="call-modal-content">
        <div class="call-info">
            <div class="caller-avatar" id="callerAvatar">
                <i class="fas fa-user"></i>
            </div>
            <div class="caller-details">
                <h3 id="callerName">Unknown Caller</h3>
                <p class="call-type" id="incomingCallType">Audio Call</p>
                <p class="call-status">Incoming call...</p>
            </div>
        </div>
        <div class="call-actions">
            <button class="call-btn decline-btn" onclick="rejectCall()" title="Decline">
                <i class="fas fa-phone-slash"></i>
            </button>
            <button class="call-btn accept-btn" onclick="acceptCall()" title="Accept">
                <i class="fas fa-phone"></i>
            </button>
        </div>
    </div>
</div>

<!-- Active Call Modal -->
<div class="call-modal active-call" id="activeCallModal" style="display: none;">
    <div class="call-modal-content">
        <div class="call-header">
            <div class="call-participant-info">
                <div class="participant-avatar" id="activeCallAvatar">
                    <i class="fas fa-user"></i>
                </div>
                <div class="participant-details">
                    <h3 id="activeCallName">Contact Name</h3>
                    <p class="call-duration" id="callDuration">00:00</p>
                    <p class="call-status" id="activeCallStatus">Connecting...</p>
                </div>
            </div>
            <button class="minimize-btn" onclick="minimizeCall()" title="Minimize">
                <i class="fas fa-minus"></i>
            </button>
        </div>

        <!-- Video Container -->
        <div class="video-container" id="videoContainer" style="display: none;">
            <video id="remoteVideo" autoplay playsinline></video>
            <video id="localVideo" autoplay playsinline muted></video>
        </div>

        <!-- Audio Indicator -->
        <div class="audio-indicator" id="audioIndicator">
            <div class="audio-wave">
                <div class="wave-bar"></div>
                <div class="wave-bar"></div>
                <div class="wave-bar"></div>
                <div class="wave-bar"></div>
                <div class="wave-bar"></div>
            </div>
        </div>

        <div class="call-controls">
            <button class="control-btn mute-btn" id="muteBtn" onclick="toggleMute()" title="Mute">
                <i class="fas fa-microphone"></i>
            </button>
            <button class="control-btn camera-btn" id="cameraBtn" onclick="toggleCamera()" title="Camera" style="display: none;">
                <i class="fas fa-video"></i>
            </button>
            <button class="control-btn speaker-btn" id="speakerBtn" onclick="toggleSpeaker()" title="Speaker">
                <i class="fas fa-volume-up"></i>
            </button>
            <button class="control-btn end-call-btn" onclick="endCall()" title="End Call">
                <i class="fas fa-phone-slash"></i>
            </button>
        </div>
    </div>
</div>

<!-- Call Minimized Bar -->
<div class="call-minimized" id="callMinimized" style="display: none;">
    <div class="minimized-info">
        <div class="minimized-avatar" id="minimizedAvatar">
            <i class="fas fa-user"></i>
        </div>
        <div class="minimized-details">
            <span id="minimizedName">Contact Name</span>
            <span class="minimized-duration" id="minimizedDuration">00:00</span>
        </div>
    </div>
    <div class="minimized-controls">
        <button class="minimized-btn" onclick="toggleMute()" id="minimizedMuteBtn">
            <i class="fas fa-microphone"></i>
        </button>
        <button class="minimized-btn expand-btn" onclick="expandCall()" title="Expand">
            <i class="fas fa-expand"></i>
        </button>
        <button class="minimized-btn end-btn" onclick="endCall()" title="End Call">
            <i class="fas fa-phone-slash"></i>
        </button>
    </div>
</div>

{% endblock %}

<!-- Toast Container -->
<div id="toastContainer" class="toast-container"></div>

{% block extra_js %}
<script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.0.1/socket.io.js"></script>
<script src="{{ url_for('static', filename='js/chat.js') }}"></script>
<script src="{{ url_for('static', filename='js/webrtc.js') }}"></script>
<script>


// Navigation Menu Toggle
document.addEventListener('DOMContentLoaded', function() {
    console.log('Navigation script loaded');
    const navToggle = document.getElementById('navToggle');
    const navMenu = document.getElementById('navMenu');

    console.log('navToggle:', navToggle);
    console.log('navMenu:', navMenu);

    if (navToggle && navMenu) {
        navToggle.addEventListener('click', function(e) {
            e.preventDefault();
            console.log('Nav toggle clicked');
            navToggle.classList.toggle('active');
            navMenu.classList.toggle('active');
            console.log('Menu active:', navMenu.classList.contains('active'));
        });

        // Close menu when clicking outside
        document.addEventListener('click', function(e) {
            if (!navToggle.contains(e.target) && !navMenu.contains(e.target)) {
                navToggle.classList.remove('active');
                navMenu.classList.remove('active');
            }
        });

        // Close menu when clicking on a link
        const navLinks = navMenu.querySelectorAll('.nav-link');
        navLinks.forEach(link => {
            link.addEventListener('click', function() {
                navToggle.classList.remove('active');
                navMenu.classList.remove('active');
            });
        });
    }
});

// Simple backup function for nav toggle
function toggleNavMenu() {
    console.log('toggleNavMenu called');
    const navToggle = document.getElementById('navToggle');
    const navMenu = document.getElementById('navMenu');

    console.log('navToggle element:', navToggle);
    console.log('navMenu element:', navMenu);

    if (navToggle && navMenu) {
        const isActive = navMenu.classList.contains('active');
        console.log('Current state - active:', isActive);

        if (isActive) {
            navToggle.classList.remove('active');
            navMenu.classList.remove('active');
            console.log('Menu closed');
        } else {
            navToggle.classList.add('active');
            navMenu.classList.add('active');
            console.log('Menu opened');
        }

        // Force style update
        navMenu.style.transform = isActive ? 'translateY(-100%)' : 'translateY(0)';
        navMenu.style.opacity = isActive ? '0' : '1';
        navMenu.style.visibility = isActive ? 'hidden' : 'visible';

    } else {
        console.error('Nav elements not found');
        console.error('navToggle:', navToggle);
        console.error('navMenu:', navMenu);
    }
}




</script>

<script>
// Simple, direct functions that will definitely work
function addContactSimple() {
    const contactCode = document.getElementById('contactCode').value.trim().toUpperCase();
    const contactName = document.getElementById('contactName').value.trim();

    if (!contactCode) {
        alert('Please enter a contact code! 💕');
        return;
    }

    if (contactCode.length !== 8) {
        alert('Contact code must be 8 characters! 🔤');
        return;
    }

    // Show loading
    const btn = event.target;
    btn.disabled = true;
    btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Adding...';

    // Send request
    fetch('/add_contact', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            contact_code: contactCode,
            contact_name: contactName
        })
    })
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            alert(result.message);
            // Close modal
            document.getElementById('addContactModal').style.display = 'none';
            document.getElementById('addContactModal').classList.remove('show');
            // Clear form
            document.getElementById('contactCode').value = '';
            document.getElementById('contactName').value = '';
            // Reload page to show new contact
            window.location.reload();
        } else {
            alert(result.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Error adding contact: ' + error.message);
    })
    .finally(() => {
        // Reset button
        btn.disabled = false;
        btn.innerHTML = '<i class="fas fa-plus"></i> Add Contact';
    });
}

// Simple function to open chat
function openChat(contactId, contactName, contactColor, profilePictureUrl, lastSeen) {
    console.log('=== OPENING CHAT ===');
    console.log('Contact ID:', contactId);
    console.log('Contact Name:', contactName);
    console.log('Last Seen:', lastSeen);

    // Check if mobile device
    const isMobile = window.innerWidth <= 768;
    console.log('Window width:', window.innerWidth, 'Is mobile:', isMobile);
    console.log('📱 Opening chat in mobile mode - contacts should be hidden');

    // Hide welcome screen
    const welcome = document.getElementById('chatWelcome');
    if (welcome) {
        welcome.style.display = 'none';
        console.log('✓ Welcome hidden');
    }

    // Show chat area
    const chatArea = document.getElementById('chatArea');
    if (chatArea) {
        chatArea.style.display = 'flex';
        chatArea.style.visibility = 'visible';
        chatArea.style.opacity = '1';

        if (isMobile) {
            // Mobile: Full screen mode
            chatArea.classList.add('chat-fullscreen');
            document.body.classList.add('chat-active');

            // Hide sidebar on mobile
            const sidebar = document.querySelector('.chat-sidebar');
            if (sidebar) {
                sidebar.style.display = 'none';
                sidebar.style.visibility = 'hidden';
            }

            // Hide contacts section specifically
            const contactsSection = document.querySelector('.contacts-section');
            if (contactsSection) {
                contactsSection.style.display = 'none';
            }

            // Show back button on mobile
            const backBtn = document.getElementById('chatBackBtn');
            if (backBtn) {
                backBtn.style.display = 'flex';
            }
        } else {
            // Desktop: Normal side-by-side layout
            chatArea.classList.remove('chat-fullscreen');

            // Ensure sidebar is visible on desktop
            const sidebar = document.querySelector('.chat-sidebar');
            if (sidebar) {
                sidebar.style.display = 'flex';
            }

            // Hide back button on desktop
            const backBtn = document.getElementById('chatBackBtn');
            if (backBtn) {
                backBtn.style.display = 'none';
            }
        }
    }

    // Update contact name in header
    const chatName = document.getElementById('chatContactName');
    if (chatName) chatName.textContent = contactName;

    // Update contact status with WhatsApp-style last seen
    const statusText = formatLastSeen(lastSeen);
    const contactStatusElement = document.getElementById('contactStatus');
    if (contactStatusElement) {
        contactStatusElement.innerHTML = statusText;
        contactStatusElement.style.color = statusText === 'online' ? '#00a884' : '#999';
        console.log('✓ Contact status set to:', statusText);
    }

    // Update avatar with profile picture
    const chatAvatar = document.getElementById('chatAvatar');
    if (chatAvatar) {
        chatAvatar.style.background = contactColor;
        chatAvatar.innerHTML = '';

        if (profilePictureUrl && profilePictureUrl.trim() !== '') {
            // Show profile picture
            const img = document.createElement('img');
            img.src = profilePictureUrl;
            img.alt = contactName;
            img.style.width = '100%';
            img.style.height = '100%';
            img.style.borderRadius = '50%';
            img.style.objectFit = 'cover';
            img.onerror = function() {
                // If image fails to load, show default icon
                chatAvatar.innerHTML = '<i class="fas fa-user"></i>';
            };
            chatAvatar.appendChild(img);
            console.log('Profile picture set:', profilePictureUrl);
        } else {
            // Show default icon
            chatAvatar.innerHTML = '<i class="fas fa-user"></i>';
            console.log('No profile picture, showing default icon');
        }
    }

    // Add sample messages
    const messagesContainer = document.getElementById('messagesContainer');
    if (messagesContainer) {
        messagesContainer.innerHTML = `
            <div class="message">
                <div class="message-bubble">
                    <div class="message-text">Hello! How are you?</div>
                    <div class="message-time">10:30 AM</div>
                </div>
            </div>
            <div class="message own">
                <div class="message-bubble">
                    <div class="message-text">Hi! I'm doing great, thanks!</div>
                    <div class="message-time">10:31 AM</div>
                </div>
            </div>
        `;
    }

    // Store current contact info globally and in chat instance
    window.currentContactId = contactId;
    window.currentContactName = contactName;
    window.currentContactColor = contactColor;
    window.currentContactProfilePicture = profilePictureUrl;

    console.log('✓ Stored contact info globally:', {
        id: contactId,
        name: contactName,
        color: contactColor,
        profilePicture: profilePictureUrl
    });

    // Set current contact in chat instance
    if (window.kawaiiChat) {
        window.kawaiiChat.currentContactId = contactId;
        window.kawaiiChat.currentContactName = contactName;
        window.kawaiiChat.currentContactColor = contactColor;
        window.kawaiiChat.currentContactProfilePicture = profilePictureUrl;
        console.log('✓ Contact set in chat instance:', contactId, contactName);
    } else {
        console.error('❌ Chat instance not found');
    }

    // Load chat history
    loadChatHistory(contactId);

    // Add manual read receipt button for testing
    addManualReadButton(contactId);

    console.log('Chat opened successfully!');
}

// Function to close chat and return to contact list
function closeChat() {
    console.log('Closing chat...');

    const isMobile = window.innerWidth <= 768;
    const chatArea = document.getElementById('chatArea');
    const welcome = document.getElementById('chatWelcome');
    const sidebar = document.querySelector('.chat-sidebar');
    const backBtn = document.getElementById('chatBackBtn');

    // Hide chat area
    if (chatArea) {
        chatArea.style.display = 'none';
        chatArea.classList.remove('chat-fullscreen');
    }

    // Remove chat active class
    document.body.classList.remove('chat-active');

    // Show welcome screen
    if (welcome) welcome.style.display = 'flex';

    // Show sidebar
    if (sidebar) {
        sidebar.style.display = 'flex';
        sidebar.style.visibility = 'visible';
    }

    // Show contacts section
    const contactsSection = document.querySelector('.contacts-section');
    if (contactsSection) {
        contactsSection.style.display = 'block';
    }

    // Hide back button
    if (backBtn) backBtn.style.display = 'none';

    console.log('Chat closed, returned to contact list');
}

// Function to show contact info (placeholder)
function showContactInfo() {
    console.log('Contact info clicked');
    // Could open contact details modal in the future
}

// Function to load chat history
function loadChatHistory(contactId) {
    console.log('📚 Loading chat history for contact:', contactId);

    fetch(`/api/messages/${contactId}`)
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            return response.json();
        })
        .then(data => {
            console.log('📚 Chat history loaded:', data);

            // Clear existing messages
            const messagesContainer = document.querySelector('.messages-container');
            if (messagesContainer) {
                messagesContainer.innerHTML = '';
            }

            // Store messages globally for reply system
            window.loadedMessages = data.messages || [];

            // Add each message to the chat
            if (data.messages && data.messages.length > 0) {
                data.messages.forEach(message => {
                    // Debug timestamp for call messages
                    if (message.message_type === 'call') {
                        console.log('📞 Loading call message from DB:', {
                            message: message.message,
                            sent_at: message.sent_at,
                            sent_at_type: typeof message.sent_at,
                            call_status: message.call_status,
                            call_duration: message.call_duration
                        });
                    }

                    addMessage(
                        message.message,
                        message.is_own,
                        message.is_own ? null : message.sender_username,
                        message.sent_at,
                        null, // no tempId for historical messages
                        message // pass full message data for ID and status
                    );
                });
                console.log(`✅ Loaded ${data.messages.length} messages`);

                // Update reactions for all loaded messages
                data.messages.forEach(message => {
                    if (message.reactions && message.reactions.length > 0) {
                        console.log(`🔄 Updating reactions for loaded message ${message.id}:`, message.reactions);
                        updateMessageReactions(message.id, message.reactions);
                    }
                });



                // Add right-click handlers to all image containers after loading
                setTimeout(() => {
                    document.querySelectorAll('.whatsapp-image-container').forEach(container => {
                        const messageId = container.getAttribute('data-message-id');
                        if (messageId) {
                            // Multiple event handlers for better compatibility
                            container.addEventListener('contextmenu', function(event) {
                                event.preventDefault();
                                event.stopPropagation();
                                showWhatsAppReactions(event, messageId);
                                return false;
                            });

                            // Alternative: mouseup with right button for better compatibility
                            container.addEventListener('mouseup', function(event) {
                                if (event.button === 2) { // Right mouse button
                                    event.preventDefault();
                                    event.stopPropagation();
                                    showWhatsAppReactions(event, messageId);
                                    return false;
                                }
                            });
                        }
                    });
                }, 100);

                // Mark messages as read after loading chat history
                console.log('📖 Auto-marking messages as read after loading chat history');
                markMessagesAsRead(contactId);
            } else {
                console.log('📭 No messages found for this contact');
            }
        })
        .catch(error => {
            console.error('❌ Error loading chat history:', error);
        });
}

// Handle Enter key press in message input
function handleEnterKey(event) {
    if (event.key === 'Enter' && !event.shiftKey) {
        event.preventDefault();
        sendMessage();
    }
}

// Main send message function - Real messenger style
function sendMessage() {
    console.log('=== SEND MESSAGE CALLED ===');

    const messageInput = document.getElementById('messageInput');
    const sendBtn = document.getElementById('sendBtn');

    if (!messageInput) {
        console.error('❌ Message input not found');
        return;
    }

    const message = messageInput.value.trim();
    console.log('Message text:', message);

    if (!message) {
        console.log('❌ Empty message');
        return;
    }

    if (!window.currentContactId) {
        console.error('❌ No contact selected');
        showToast('Please select a contact first!', 'error');
        return;
    }

    // Disable send button to prevent double sending
    if (sendBtn) {
        sendBtn.disabled = true;
        sendBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
    }

    // Clear input immediately for better UX
    messageInput.value = '';

    // Clear reply if replying
    if (typeof currentReplyToMessage !== 'undefined' && currentReplyToMessage && typeof cancelReply === 'function') {
        cancelReply();
    }

    // Add message to chat immediately (optimistic UI)
    const tempMessageId = 'temp_' + Date.now();
    addMessage(message, true, null, null, tempMessageId);

    console.log('Sending to contact:', window.currentContactId);

    // Prepare message data
    const messageData = {
        receiver_id: window.currentContactId,
        message: message,
        temp_id: tempMessageId
    };

    // Add reply information if replying
    if (typeof currentReplyToMessage !== 'undefined' && currentReplyToMessage) {
        messageData.reply_to_message_id = currentReplyToMessage.id;
    }

    // Try Socket.IO first for real-time messaging
    if (socket && socket.connected) {
        console.log('✅ Sending via Socket.IO');
        socket.emit('send_message', messageData);

        // Re-enable send button
        if (sendBtn) {
            sendBtn.disabled = false;
            sendBtn.innerHTML = '<i class="fas fa-paper-plane"></i>';
        }
        return;
    }

    // Fallback: Send message via HTTP
    console.log('📡 Using HTTP fallback');

    fetch('/send_message', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(messageData)
    })
    .then(response => response.json())
    .then(data => {
        console.log('✅ Message sent via HTTP:', data);
        if (data.success) {
            // Update the temporary message with real data
            updateTempMessage(tempMessageId, data.message);
            showToast('Message sent! 💌', 'success');
        } else {
            // Remove the temporary message on failure
            removeTempMessage(tempMessageId);
            showToast('Failed to send: ' + data.message, 'error');
        }
    })
    .catch(error => {
        console.error('❌ Send error:', error);
        removeTempMessage(tempMessageId);
        showToast('Failed to send message', 'error');
    })
    .finally(() => {
        // Re-enable send button
        if (sendBtn) {
            sendBtn.disabled = false;
            sendBtn.innerHTML = '<i class="fas fa-paper-plane"></i>';
        }
    });
}

// Function to add message to chat display - Real messenger style
function addMessage(message, isOwn, senderName = null, timestamp = null, tempId = null, messageData = null) {
    console.log('➕ Adding message:', { message, isOwn, senderName, timestamp, tempId, messageData });

    const messagesContainer = document.querySelector('.messages-container');
    if (!messagesContainer) {
        console.error('❌ Messages container not found');
        return;
    }

    const messageDiv = document.createElement('div');
    messageDiv.className = `message ${isOwn ? 'own' : ''}`;

    // Add temp ID for temporary messages
    if (tempId) {
        messageDiv.setAttribute('data-temp-id', tempId);
        messageDiv.classList.add('message-sending');
    }

    // Add message ID for real messages
    if (messageData?.id) {
        messageDiv.setAttribute('data-message-id', messageData.id);

        // Add long press handlers for selection
        messageDiv.addEventListener('touchstart', (e) => handleLongPressStart(e, messageData.id));
        messageDiv.addEventListener('touchend', handleLongPressEnd);
        messageDiv.addEventListener('touchcancel', handleLongPressEnd);


    }

    // Use provided timestamp or current time
    let timeStr;
    if (timestamp) {
        console.log('🕐 Processing timestamp for message:', timestamp, 'Type:', typeof timestamp);
        const date = new Date(timestamp);
        console.log('🕐 Parsed date:', date, 'Valid:', !isNaN(date.getTime()));
        timeStr = date.toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});
        console.log('🕐 Formatted time string:', timeStr);
    } else {
        console.log('🕐 No timestamp provided, using current time');
        const now = new Date();
        timeStr = now.toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});
    }

    // Special debugging for call messages
    if (messageData?.message_type === 'call') {
        console.log('📞 Call message timestamp debug:', {
            timestamp: timestamp,
            messageData_sent_at: messageData.sent_at,
            timeStr: timeStr,
            messageData: messageData
        });
    }

    // Add sender name for received messages
    let senderNameHtml = '';
    if (!isOwn && senderName) {
        senderNameHtml = `<div class="message-sender">${senderName}</div>`;
    }

    // Add delivery status for own messages ONLY
    let statusHtml = '';
    if (isOwn) {
        if (tempId) {
            statusHtml = `<div class="message-status sending"></div>`;
        } else {
            // Proper read receipt logic - only show double checkmarks if read_at is explicitly set and not null/empty
            const isRead = messageData?.read_at &&
                          messageData.read_at !== null &&
                          messageData.read_at !== '' &&
                          messageData.read_at !== 'null';

            // Debug read status for all messages
            console.log('🔍 Read status debug for message:', {
                id: messageData?.id,
                message: messageData?.message,
                read_at: messageData?.read_at,
                read_at_type: typeof messageData?.read_at,
                isRead: isRead,
                sender_id: messageData?.sender_id,
                receiver_id: messageData?.receiver_id
            });

            if (isRead) {
                console.log('📖 Message is READ - showing double checkmark:', messageData.id);
                statusHtml = `<div class="message-status read"></div>`;
            } else {
                console.log('📤 Message is SENT - showing single checkmark:', messageData?.id);
                statusHtml = `<div class="message-status sent"></div>`;
            }
        }
    }
    // No status shown for received messages

    // Generate reply content if this is a reply
    let replyContent = '';
    if (messageData?.replied_message) {
        const repliedMsg = messageData.replied_message;
        let repliedContent = '';

        if (repliedMsg.message_type === 'image') {
            repliedContent = '<i class="fas fa-image"></i> Photo';
        } else {
            repliedContent = escapeHtml(repliedMsg.message || '');
        }

        replyContent = `
            <div class="message-reply" onclick="scrollToMessage(${repliedMsg.id})">
                <div class="reply-bar"></div>
                <div class="reply-content">
                    <div class="reply-sender">${escapeHtml(repliedMsg.sender_username || 'Unknown')}</div>
                    <div class="reply-text">${repliedContent}</div>
                </div>
            </div>
        `;
    }

    // Generate message content based on type
    let messageContent = '';
    if (messageData?.message_type === 'call') {
        // Handle call history messages - WhatsApp style
        console.log('📞 Creating call message with data:', messageData);
        const messageId = messageData.id || messageData.message_id || 0;

        // Determine call direction and status
        const callStatus = messageData.call_status || 'unknown';
        const callDuration = messageData.call_duration;
        const isVideoCall = message.includes('📹');
        const callIcon = isVideoCall ? '📹' : '📞';

        // Generate WhatsApp-style message text based on perspective
        let displayMessage = '';
        let callClass = 'call-message';

        if (callStatus === 'missed') {
            displayMessage = `${callIcon} Missed call`;
            callClass += ' missed-call';
        } else if (callStatus === 'rejected') {
            if (isOwn) {
                displayMessage = `${callIcon} Call declined`;
            } else {
                displayMessage = `${callIcon} Declined call`;
            }
            callClass += ' declined-call';
        } else if (callStatus === 'ended' && callDuration) {
            const durationStr = formatCallDuration(callDuration);
            if (isOwn) {
                displayMessage = `${callIcon} Outgoing call • ${durationStr}`;
            } else {
                displayMessage = `${callIcon} Incoming call • ${durationStr}`;
            }
            callClass += ' completed-call';
        } else {
            // Fallback - parse from message text
            if (message.includes('Missed call')) {
                displayMessage = message;
                callClass += ' missed-call';
            } else if (message.includes('Call declined')) {
                displayMessage = message;
                callClass += ' declined-call';
            } else if (message.includes('Call •')) {
                // Extract duration from message
                const durationMatch = message.match(/Call • (.+)/);
                const duration = durationMatch ? durationMatch[1] : '';
                if (isOwn) {
                    displayMessage = `${callIcon} Outgoing call • ${duration}`;
                } else {
                    displayMessage = `${callIcon} Incoming call • ${duration}`;
                }
                callClass += ' completed-call';
            } else {
                displayMessage = message;
                callClass += ' completed-call';
            }
        }

        messageContent = `
            <div class="${callClass}" data-message-id="${messageId}">
                <div class="call-info">
                    <span class="call-icon">${callIcon}</span>
                    <div class="call-details">
                        <span class="call-text">${escapeHtml(displayMessage)}</span>
                        <span class="call-time">${timeStr}</span>
                    </div>
                </div>
            </div>
        `;
    } else if (messageData?.message_type === 'image' && messageData?.media_url) {
        console.log('🖼️ Creating image message with data:', messageData);
        const messageId = messageData.id || messageData.message_id || 0;
        console.log('📝 Using message ID:', messageId);

        const reactions = messageData.reactions || [];
        console.log('🎨 Message reactions for ID', messageId, ':', reactions);
        const reactionsHtml = generateReactionsHtml(reactions, messageId);
        console.log('🎨 Generated reactions HTML:', reactionsHtml);
        messageContent = `
            <div class="whatsapp-image-container" data-message-id="${messageId}"
                 oncontextmenu="showWhatsAppReactions(event, ${messageId}); return false;"
                 ontouchstart="handleTouchStart(event, ${messageId})"
                 ontouchend="handleTouchEnd(event)">
                <div class="image-wrapper">
                    <img src="${messageData.media_url}" alt="Photo" class="whatsapp-image"
                         onclick="openWhatsAppImageViewer('${messageData.media_url}', '${messageData.sender_username || 'You'}', '${messageData.sent_at || ''}')" loading="lazy">
                    <div class="image-overlay">
                        <div class="image-time">${formatTime(messageData.sent_at)}</div>
                    </div>
                </div>
                <div class="reactions-container">
                    ${reactionsHtml}
                </div>
            </div>
        `;
    } else if (messageData?.message_type === 'file' && messageData?.file_url) {
        console.log('📎 Creating file message with data:', messageData);
        console.log('🔍 Full messageData object:', JSON.stringify(messageData, null, 2));
        const messageId = messageData.id || messageData.message_id || 0;
        const fileType = getFileTypeIcon(messageData.file_type || '');
        const fileSize = formatFileSize(messageData.file_size || 0);
        // Check for video files - backend stores as 'video' in media_type, or check MIME type in file_type
        const isVideo = (messageData.media_type === 'video') ||
                       (messageData.file_type && messageData.file_type.startsWith('video/'));

        if (isVideo) {
            // Debug: Log video data
            console.log('🎬 Creating video message:', {
                file_url: messageData.file_url,
                file_name: messageData.file_name,
                file_type: messageData.file_type,
                file_size: messageData.file_size
            });

            // Video message with thumbnail and play button
            messageContent = `
                <div class="video-message-container" data-message-id="${messageId}">
                    <div class="video-thumbnail" onclick="openVideoPlayer('${messageData.file_url}', '${messageData.file_name || 'Video'}', '${messageData.sender_username || 'You'}', '${messageData.sent_at || ''}')">
                        ${messageData.file_url && !messageData.file_url.includes('via.placeholder.com') ?
                            `<video class="video-preview" preload="metadata" onloadedmetadata="updateVideoDuration(this, '${messageId}')" onerror="handleVideoThumbnailError(this)">
                                <source src="${messageData.file_url}#t=1" type="${messageData.file_type}">
                            </video>` :
                            `<div class="video-thumbnail-fallback">
                                <div class="video-fallback-icon">
                                    <i class="fas fa-video"></i>
                                </div>
                                <div class="video-fallback-text">Video</div>
                            </div>`
                        }
                        <div class="video-overlay">
                            <div class="play-button">
                                <i class="fas fa-play"></i>
                            </div>
                            <div class="video-duration" id="duration-${messageId}">
                                <i class="fas fa-video"></i>
                            </div>
                        </div>
                    </div>
                    <div class="video-info">
                        <div class="video-name">${escapeHtml(messageData.file_name || 'Video')}</div>
                        <div class="video-size">${fileSize}</div>
                    </div>
                    ${message ? `<div class="file-caption">${escapeHtml(message)}</div>` : ''}
                </div>
            `;
        } else {
            // Regular file message
            messageContent = `
                <div class="file-message-container" data-message-id="${messageId}">
                    <div class="file-message-content" onclick="downloadFile('${messageData.file_url}', '${messageData.file_name || 'file'}')">
                        <div class="file-icon">
                            <i class="${fileType.icon}" style="color: ${fileType.color}"></i>
                        </div>
                        <div class="file-details">
                            <div class="file-name">${escapeHtml(messageData.file_name || 'Unknown File')}</div>
                            <div class="file-meta">${fileType.name} • ${fileSize}</div>
                        </div>
                        <div class="file-download">
                            <i class="fas fa-download"></i>
                        </div>
                    </div>
                    ${message ? `<div class="file-caption">${escapeHtml(message)}</div>` : ''}
                </div>
            `;
        }
    } else {
        messageContent = `<div class="message-text">${escapeHtml(message)}</div>`;
    }

    messageDiv.innerHTML = `
        <div class="message-bubble">
            ${senderNameHtml}
            ${replyContent}
            ${messageContent}
            <div class="message-footer">
                <span class="message-time">${timeStr}</span>
                ${statusHtml}
            </div>
        </div>
    `;

    messagesContainer.appendChild(messageDiv);

    // Smooth scroll to bottom
    setTimeout(() => {
        messagesContainer.scrollTo({
            top: messagesContainer.scrollHeight,
            behavior: 'smooth'
        });
    }, 100);

    console.log('✅ Message added to chat:', { message, isOwn, senderName, timestamp, tempId });
    return messageDiv;
}

// Helper function to escape HTML
function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

// Update temporary message with real data
function updateTempMessage(tempId, messageData) {
    const tempMessage = document.querySelector(`[data-temp-id="${tempId}"]`);
    if (tempMessage) {
        tempMessage.classList.remove('message-sending');
        tempMessage.removeAttribute('data-temp-id');

        // Set the real message ID
        if (messageData.id) {
            tempMessage.setAttribute('data-message-id', messageData.id);
            console.log('✅ Set message ID:', messageData.id, 'for temp message:', tempId);
        }

        // Update status icon to show sent (single checkmark)
        const statusElement = tempMessage.querySelector('.message-status');
        if (statusElement) {
            statusElement.innerHTML = '';
            statusElement.className = 'message-status sent';
        }

        console.log('✅ Updated temporary message to sent:', tempId);
    }
}

// Update message status (sent -> delivered -> read) - ONLY for own messages
function updateMessageStatus(messageId, tempId, status) {
    // Find message by temp_id or message_id
    let messageElement;
    if (tempId) {
        messageElement = document.querySelector(`[data-temp-id="${tempId}"]`);
    }
    if (!messageElement && messageId) {
        messageElement = document.querySelector(`[data-message-id="${messageId}"]`);
    }

    if (messageElement) {
        // Only update status for own messages (messages with .own class)
        if (!messageElement.classList.contains('own')) {
            console.log('⚠️ Skipping status update for received message:', messageId);
            return;
        }

        const statusElement = messageElement.querySelector('.message-status');
        if (statusElement) {
            if (status === 'delivered') {
                statusElement.innerHTML = '';
                statusElement.className = 'message-status delivered';
                console.log('📨 Updated message to delivered:', messageId);
            } else if (status === 'read') {
                statusElement.innerHTML = '';
                statusElement.className = 'message-status read';
                console.log('📖 Updated message to read:', messageId);
            }
        }
    }
}

// Mark messages as read when user opens chat
function markMessagesAsRead(senderId) {
    if (socket && socket.connected && senderId) {
        console.log('📖 Marking messages as read from sender:', senderId);
        socket.emit('mark_messages_read', { sender_id: senderId });
    }
}

// Mark only visible messages as read (more accurate read receipts)
function markVisibleMessagesAsRead(senderId) {
    if (!socket || !socket.connected || !senderId) {
        return;
    }

    const messagesContainer = document.querySelector('.messages-container');
    if (!messagesContainer) {
        return;
    }

    // Get all unread messages from this sender
    const unreadMessages = document.querySelectorAll('.message:not(.own)');
    const visibleUnreadMessages = [];

    unreadMessages.forEach(messageElement => {
        const messageId = messageElement.getAttribute('data-message-id');
        if (messageId && isElementInViewport(messageElement)) {
            visibleUnreadMessages.push(parseInt(messageId));
        }
    });

    if (visibleUnreadMessages.length > 0) {
        console.log('📖 Marking visible messages as read:', visibleUnreadMessages);
        socket.emit('mark_specific_messages_read', {
            sender_id: senderId,
            message_ids: visibleUnreadMessages
        });
    }
}

// Check if an element is in the viewport (at least 50% visible)
function isElementInViewport(element) {
    const rect = element.getBoundingClientRect();
    const messagesContainer = document.querySelector('.messages-container');
    if (!messagesContainer) return false;

    const containerRect = messagesContainer.getBoundingClientRect();

    // Check if at least 50% of the element is visible
    const elementHeight = rect.bottom - rect.top;
    const visibleTop = Math.max(rect.top, containerRect.top);
    const visibleBottom = Math.min(rect.bottom, containerRect.bottom);
    const visibleHeight = Math.max(0, visibleBottom - visibleTop);

    return (visibleHeight / elementHeight) >= 0.5;
}

// Add manual read button for testing
function addManualReadButton(contactId) {
    // Remove existing button if any
    const existingBtn = document.getElementById('manualReadBtn');
    if (existingBtn) {
        existingBtn.remove();
    }

    // Create manual read button
    const readBtn = document.createElement('button');
    readBtn.id = 'manualReadBtn';
    readBtn.innerHTML = '📖 Mark Messages as Read';
    readBtn.style.cssText = `
        position: fixed;
        top: 70px;
        right: 20px;
        background: #ff6b9d;
        color: white;
        border: none;
        padding: 10px 15px;
        border-radius: 8px;
        cursor: pointer;
        z-index: 1000;
        font-size: 14px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.2);
    `;

    readBtn.onclick = function() {
        console.log('📖 Manual read button clicked for contact:', contactId);
        markMessagesAsRead(contactId);
        showToast('Messages marked as read! ✓✓', 'success');

        // Refresh the chat to show updated read status
        setTimeout(() => {
            loadChatHistory(contactId);
        }, 500);
    };

    document.body.appendChild(readBtn);
    console.log('✅ Added manual read button');
}

// Show media modal (photo upload)
function showMediaModal() {
    if (!window.currentContactId) {
        showToast('Please select a contact first!', 'error');
        return;
    }

    const imageInput = document.getElementById('imageInput');
    if (imageInput) {
        imageInput.click();
    }
}

// Handle image upload
function handleImageUpload(event) {
    const file = event.target.files[0];
    if (!file) return;

    // Check file size (max 10MB)
    if (file.size > 10 * 1024 * 1024) {
        showToast('Image too large! Maximum size is 10MB.', 'error');
        return;
    }

    // Check file type
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
    if (!allowedTypes.includes(file.type)) {
        showToast('Invalid file type! Only images are allowed.', 'error');
        return;
    }

    // Show loading message
    const tempMessageId = 'temp_' + Date.now();
    addMessage('📷 Uploading photo...', true, null, null, tempMessageId);

    // Create FormData
    const formData = new FormData();
    formData.append('image', file);
    formData.append('receiver_id', window.currentContactId);

    // Upload image
    fetch('/upload_image', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        // Remove temporary message
        removeTempMessage(tempMessageId);

        if (data.success) {
            // Add the actual image message
            addMessage(data.message.message, true, null, null, null, data.message);
            showToast('Photo sent! 📷', 'success');
        } else {
            showToast('Failed to send photo: ' + data.message, 'error');
        }
    })
    .catch(error => {
        console.error('❌ Image upload error:', error);
        removeTempMessage(tempMessageId);
        showToast('Failed to upload photo', 'error');
    });

    // Clear the input
    event.target.value = '';
}

// Remove temporary message
function removeTempMessage(tempId) {
    const tempMessage = document.querySelector(`[data-temp-id="${tempId}"]`);
    if (tempMessage) {
        tempMessage.remove();
    }
}

// Open WhatsApp-style image viewer
function openWhatsAppImageViewer(imageUrl, senderName, sentAt) {
    const viewer = document.getElementById('whatsappImageViewer');
    const viewerImage = document.getElementById('viewerImage');
    const senderNameEl = document.getElementById('imageSenderName');
    const imageDateEl = document.getElementById('imageDate');

    if (viewer && viewerImage) {
        viewerImage.src = imageUrl;
        senderNameEl.textContent = senderName || 'Contact';
        imageDateEl.textContent = formatDate(sentAt) || 'Today';
        viewer.style.display = 'flex';
        document.body.style.overflow = 'hidden';

        // Store current message ID for reactions
        window.currentViewerMessageId = getCurrentMessageIdFromImage(imageUrl);

        // Load reactions for this image
        loadImageReactions(window.currentViewerMessageId);
    }
}

// Close WhatsApp image viewer
function closeWhatsAppImageViewer() {
    const viewer = document.getElementById('whatsappImageViewer');
    if (viewer) {
        viewer.style.display = 'none';
        document.body.style.overflow = 'auto';
        window.currentViewerMessageId = null;
    }
}

// Get message ID from image URL (helper function)
function getCurrentMessageIdFromImage(imageUrl) {
    // Find the message element with this image URL
    const images = document.querySelectorAll('.whatsapp-image');
    for (let img of images) {
        if (img.src === imageUrl) {
            const container = img.closest('[data-message-id]');
            if (container) {
                return parseInt(container.getAttribute('data-message-id'));
            }
        }
    }
    return null;
}

// Format date for image viewer
function formatDate(dateString) {
    if (!dateString) return 'Today';

    const date = new Date(dateString);
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const messageDate = new Date(date.getFullYear(), date.getMonth(), date.getDate());

    if (messageDate.getTime() === today.getTime()) {
        return 'Today';
    } else if (messageDate.getTime() === today.getTime() - 86400000) {
        return 'Yesterday';
    } else {
        return date.toLocaleDateString();
    }
}

// Format time for image overlay
function formatTime(dateString) {
    if (!dateString) return '';

    const date = new Date(dateString);
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
}

// Keyboard shortcuts for image viewer
document.addEventListener('keydown', function(event) {
    if (event.key === 'Escape') {
        closeWhatsAppImageViewer();
        closeReactionBar();
        closeMoreReactions();
    }

    // If image viewer is open
    if (document.getElementById('whatsappImageViewer').style.display === 'flex') {
        if (event.key === 'Enter' || event.key === ' ') {
            event.preventDefault();
            if (window.currentViewerMessageId) {
                showWhatsAppReactions(event, window.currentViewerMessageId);
            }
        }
    }
});

// Show toast notification
function showToast(message, type = 'info') {
    const container = document.getElementById('toastContainer');
    if (!container) return;

    const toast = document.createElement('div');
    toast.className = `toast ${type}`;

    // Add icon based on type
    let icon = '';
    switch(type) {
        case 'success': icon = '✅'; break;
        case 'error': icon = '❌'; break;
        case 'info': icon = 'ℹ️'; break;
        default: icon = 'ℹ️';
    }

    toast.innerHTML = `${icon} ${message}`;
    container.appendChild(toast);

    // Auto remove after 3 seconds
    setTimeout(() => {
        if (toast.parentNode) {
            toast.style.animation = 'toastSlideOut 0.3s ease-in forwards';
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.remove();
                }
            }, 300);
        }
    }, 3000);
}

// Generate reactions HTML (WhatsApp style)
function generateReactionsHtml(reactions, messageId) {
    if (!reactions || reactions.length === 0) {
        return '';
    }

    let reactionsHtml = '<div class="whatsapp-reactions">';
    reactions.forEach(reaction => {
        const title = `${reaction.usernames.join(', ')} reacted with ${reaction.emoji}`;
        reactionsHtml += `
            <div class="whatsapp-reaction-bubble" title="${title}" onclick="showReactionDetails(${messageId}, '${reaction.emoji}')">
                <span class="reaction-emoji">${reaction.emoji}</span>
                <span class="reaction-count">${reaction.count}</span>
            </div>
        `;
    });
    reactionsHtml += '</div>';

    return reactionsHtml;
}

// WhatsApp-style reaction system
let currentReactionMessageId = null;
let touchTimer = null;

// Handle touch start for long press
function handleTouchStart(event, messageId) {
    touchTimer = setTimeout(() => {
        showWhatsAppReactions(event, messageId);
    }, 500); // 500ms long press
}

// Handle touch end
function handleTouchEnd(event) {
    if (touchTimer) {
        clearTimeout(touchTimer);
        touchTimer = null;
    }
}

// Show WhatsApp-style reaction bar
function showWhatsAppReactions(event, messageId) {
    event.preventDefault();
    event.stopPropagation();

    console.log('🎯 Showing WhatsApp reactions for message:', messageId);

    // Try to get message ID from the DOM element if not provided
    if (!messageId || messageId === 0 || messageId === null || messageId === undefined) {
        const container = event.target.closest('[data-message-id]');
        if (container) {
            messageId = container.getAttribute('data-message-id');
            console.log('📝 Got message ID from DOM:', messageId);
        }
    }

    // Also try to get from currentTarget
    if (!messageId || messageId === 0 || messageId === null || messageId === undefined) {
        const currentContainer = event.currentTarget;
        if (currentContainer && currentContainer.hasAttribute('data-message-id')) {
            messageId = currentContainer.getAttribute('data-message-id');
            console.log('📝 Got message ID from currentTarget:', messageId);
        }
    }

    if (!messageId || messageId === 0 || messageId === null || messageId === undefined || messageId === '') {
        console.error('❌ Invalid message ID for reactions:', messageId);
        return;
    }

    currentReactionMessageId = parseInt(messageId);
    console.log('✅ Set currentReactionMessageId to:', currentReactionMessageId);
    const reactionBar = document.getElementById('whatsappReactionBar');

    if (reactionBar) {
        // Position reaction bar near the click/touch
        let left = event.pageX || event.clientX || window.innerWidth / 2;
        let top = event.pageY || event.clientY || window.innerHeight / 2;

        left = left - 150; // Center the bar
        top = top - 60;    // Above the click

        // Keep bar on screen
        if (left < 10) left = 10;
        if (left + 300 > window.innerWidth) left = window.innerWidth - 310;
        if (top < 10) top = (event.pageY || event.clientY || window.innerHeight / 2) + 20; // Below if no space above

        reactionBar.style.left = left + 'px';
        reactionBar.style.top = top + 'px';
        reactionBar.style.display = 'block';
        reactionBar.style.zIndex = '9999';

        console.log('✅ WhatsApp reaction bar shown at:', left, top);

        // Close bar when clicking outside
        setTimeout(() => {
            document.addEventListener('click', closeReactionBar, { once: true });
        }, 100);
    } else {
        console.error('❌ Reaction bar not found');
    }
}

// Close reaction bar
function closeReactionBar(preserveMessageId = false) {
    const reactionBar = document.getElementById('whatsappReactionBar');
    if (reactionBar) {
        reactionBar.style.display = 'none';
    }
    if (!preserveMessageId) {
        currentReactionMessageId = null;
    }
}

// Show more reactions modal
function showMoreReactions() {
    // Close reaction bar but preserve message ID
    closeReactionBar(true);

    const modal = document.getElementById('moreReactionsModal');
    if (modal) {
        modal.style.display = 'flex';
    }


}

// Close more reactions modal
function closeMoreReactions() {
    const modal = document.getElementById('moreReactionsModal');
    if (modal) {
        modal.style.display = 'none';
    }
    currentReactionMessageId = null;
}

// Add quick reaction (from WhatsApp bar or modal)
function addQuickReaction(emoji) {
    console.log('👍 Adding quick reaction:', emoji, 'to message:', currentReactionMessageId);

    if (!currentReactionMessageId) {
        console.error('❌ No message ID for reaction');
        showToast('Error: No message selected', 'error');
        return;
    }

    // Close all reaction UIs
    closeReactionBar();
    closeMoreReactions();

    // Add reaction with animation
    const messageElement = document.querySelector(`[data-message-id="${currentReactionMessageId}"]`);
    if (messageElement) {
        // Show temporary reaction animation
        showReactionAnimation(messageElement, emoji);
    }

    console.log('📡 Sending reaction request...');

    fetch('/add_reaction', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            message_id: currentReactionMessageId,
            emoji: emoji
        })
    })
    .then(response => {
        console.log('📡 Reaction response status:', response.status);
        alert('Response status: ' + response.status);
        if (!response.ok) {
            throw new Error('Network response was not ok');
        }
        return response.json();
    })
    .then(data => {
        console.log('📡 Reaction response data:', data);
        alert('Response data: ' + JSON.stringify(data));
        if (data.success) {
            updateMessageReactions(currentReactionMessageId, data.reactions);
            showToast(`Reacted with ${emoji}!`, 'success');
        } else {
            alert('Error: ' + data.message);
            showToast('Failed to add reaction: ' + (data.message || 'Unknown error'), 'error');
        }
    })
    .catch(error => {
        console.error('❌ Error adding reaction:', error);
        alert('Fetch error: ' + error.message);
        showToast('Failed to add reaction', 'error');
    });
}

// Update message reactions display
function updateMessageReactions(messageId, reactions) {
    console.log('🔄 Updating reactions for message:', messageId, 'reactions:', reactions);

    // Update reactions in chat
    const imageMessage = document.querySelector(`[data-message-id="${messageId}"]`);
    if (imageMessage) {
        // Find or create reactions container
        let reactionsContainer = imageMessage.querySelector('.reactions-container');
        if (!reactionsContainer) {
            reactionsContainer = document.createElement('div');
            reactionsContainer.className = 'reactions-container';
            imageMessage.appendChild(reactionsContainer);
        }

        // Remove existing reactions
        const existingReactions = reactionsContainer.querySelector('.whatsapp-reactions');
        if (existingReactions) {
            existingReactions.remove();
        }

        // Add new reactions
        if (reactions && reactions.length > 0) {
            const reactionsHtml = generateReactionsHtml(reactions, messageId);
            reactionsContainer.innerHTML = reactionsHtml;
        }
    }

    // Update reactions in image viewer if open
    if (window.currentViewerMessageId === messageId) {
        displayImageViewerReactions(reactions);
    }
}

// Show reaction animation
function showReactionAnimation(messageElement, emoji) {
    const animationDiv = document.createElement('div');
    animationDiv.className = 'reaction-animation';
    animationDiv.textContent = emoji;
    animationDiv.style.cssText = `
        position: absolute;
        font-size: 32px;
        z-index: 1000;
        pointer-events: none;
        animation: reactionPop 1s ease-out forwards;
    `;

    const rect = messageElement.getBoundingClientRect();
    animationDiv.style.left = (rect.left + rect.width / 2 - 16) + 'px';
    animationDiv.style.top = (rect.top + rect.height / 2 - 16) + 'px';

    document.body.appendChild(animationDiv);

    setTimeout(() => {
        if (animationDiv.parentNode) {
            animationDiv.remove();
        }
    }, 1000);
}

// Load reactions for image viewer
function loadImageReactions(messageId) {
    if (!messageId) return;

    fetch(`/get_reactions/${messageId}`)
        .then(response => response.json())
        .then(data => {
            displayImageViewerReactions(data.reactions || []);
        })
        .catch(error => {
            console.error('Error loading reactions:', error);
        });
}

// Display reactions in image viewer footer
function displayImageViewerReactions(reactions) {
    const footer = document.getElementById('imageViewerReactions');
    if (!footer) return;

    if (reactions.length === 0) {
        footer.innerHTML = '<div style="color: rgba(255,255,255,0.5); font-size: 13px;">No reactions yet</div>';
        return;
    }

    let reactionsHtml = '<div class="viewer-reactions">';
    reactions.forEach(reaction => {
        const title = `${reaction.usernames.join(', ')} reacted with ${reaction.emoji}`;
        reactionsHtml += `
            <div class="viewer-reaction-item" title="${title}">
                <span class="reaction-emoji">${reaction.emoji}</span>
                <span class="reaction-count">${reaction.count}</span>
                <span class="reaction-users">${reaction.usernames.slice(0, 3).join(', ')}${reaction.usernames.length > 3 ? '...' : ''}</span>
            </div>
        `;
    });
    reactionsHtml += '</div>';
    footer.innerHTML = reactionsHtml;
}

// Show reaction details modal
function showReactionDetails(messageId, emoji) {
    // This could show who reacted with what emoji
    console.log('Show reaction details for message:', messageId, 'emoji:', emoji);
}

// Download image function
function downloadImage() {
    const viewerImage = document.getElementById('viewerImage');
    if (viewerImage && viewerImage.src) {
        const link = document.createElement('a');
        link.href = viewerImage.src;
        link.download = 'image.jpg';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        showToast('Image download started! 📥', 'success');
    }
}

// Remove temporary message on failure
function removeTempMessage(tempId) {
    const tempMessage = document.querySelector(`[data-temp-id="${tempId}"]`);
    if (tempMessage) {
        tempMessage.style.animation = 'fadeOut 0.3s ease-out';
        setTimeout(() => {
            tempMessage.remove();
        }, 300);
        console.log('❌ Removed temporary message:', tempId);
    }
}

// Toast notification system
function showToast(message, type = 'info') {
    // Remove existing toast
    const existingToast = document.querySelector('.toast-notification');
    if (existingToast) {
        existingToast.remove();
    }

    const toast = document.createElement('div');
    toast.className = `toast-notification toast-${type}`;

    const icon = type === 'success' ? '✅' : type === 'error' ? '❌' : 'ℹ️';

    toast.innerHTML = `
        <div class="toast-content">
            <span class="toast-icon">${icon}</span>
            <span class="toast-message">${message}</span>
        </div>
    `;

    document.body.appendChild(toast);

    // Animate in
    setTimeout(() => {
        toast.classList.add('toast-show');
    }, 100);

    // Auto remove after 3 seconds
    setTimeout(() => {
        toast.classList.remove('toast-show');
        setTimeout(() => {
            if (toast.parentNode) {
                toast.remove();
            }
        }, 300);
    }, 3000);
}

// Floating emojis for chat background
function createFloatingEmoji() {
    const emojis = ['❤️', '💕', '💖', '💗', '💘', '💝', '💞', '💟', '🤍', '🧡', '💛', '💚', '💙', '💜', '🖤', '🤎', '😍', '🥰', '😘', '💋', '🌸', '🌺', '🌹', '🌷', '✨', '💫', '⭐', '🌟'];
    const container = document.getElementById('floatingEmojis');

    if (!container) return;

    const emoji = document.createElement('div');
    emoji.className = 'floating-emoji';
    emoji.textContent = emojis[Math.floor(Math.random() * emojis.length)];
    emoji.style.left = Math.random() * 100 + '%';
    emoji.style.animationDelay = Math.random() * 2 + 's';
    emoji.style.animationDuration = (Math.random() * 4 + 8) + 's';
    emoji.style.fontSize = (Math.random() * 8 + 20) + 'px'; // Random size 20-28px

    container.appendChild(emoji);

    // Remove emoji after animation
    setTimeout(() => {
        if (emoji.parentNode) {
            emoji.remove();
        }
    }, 12000);
}

// Start floating emojis
function startFloatingEmojis() {
    // Create initial emojis
    for (let i = 0; i < 8; i++) {
        setTimeout(() => createFloatingEmoji(), i * 500);
    }

    // Continue creating emojis more frequently
    const emojiInterval = setInterval(createFloatingEmoji, 1500);

    // Store interval globally so it can be restarted if needed
    window.emojiInterval = emojiInterval;

    // Ensure emojis keep flowing even with many messages
    setInterval(() => {
        const container = document.getElementById('floatingEmojis');
        if (container && container.children.length < 3) {
            // If too few emojis, create more
            for (let i = 0; i < 3; i++) {
                setTimeout(() => createFloatingEmoji(), i * 200);
            }
        }
    }, 5000);
}

// Socket.IO connection for real-time messaging
let socket;

// Initialize Socket.IO connection
function initializeSocket() {
    console.log('🔌 INITIALIZING SOCKET.IO CONNECTION...');

    try {
        // Create socket with enhanced configuration
        socket = io({
            transports: ['websocket', 'polling'],
            timeout: 20000,
            reconnection: true,
            reconnectionDelay: 1000,
            reconnectionAttempts: 5,
            maxReconnectionAttempts: 5
        });

        console.log('Socket.IO instance created:', socket);

        // Make socket globally available
        window.socket = socket;

        socket.on('connect', function() {
            console.log('✅ CONNECTED TO SOCKET.IO SERVER');
            console.log('Socket ID:', socket.id);
            console.log('Socket connected:', socket.connected);
            console.log('Transport:', socket.io.engine.transport.name);

            // Update connection status indicator
            updateConnectionStatus('connected');

            // Update my own status to online - REAL STATUS
            setRealStatus('online');

            // Show connection success toast
            if (window.showToast) {
                window.showToast('Connected to server! 🌸', 'success');
            }
        });

        socket.on('disconnect', function(reason) {
            console.log('❌ DISCONNECTED FROM SOCKET.IO SERVER');
            console.log('Disconnect reason:', reason);

            // Update connection status indicator
            updateConnectionStatus('disconnected');

            // Update my own status to offline - REAL STATUS
            setRealStatus('offline');

            // Show disconnect warning
            if (window.showToast) {
                window.showToast('Connection lost. Reconnecting...', 'warning');
            }
        });

        socket.on('reconnect', function(attemptNumber) {
            console.log('🔄 RECONNECTED TO SOCKET.IO SERVER');
            console.log('Reconnection attempt:', attemptNumber);

            if (window.showToast) {
                window.showToast('Reconnected to server! ✨', 'success');
            }
        });

        socket.on('reconnect_error', function(error) {
            console.error('❌ RECONNECTION ERROR:', error);
        });

        socket.on('reconnect_failed', function() {
            console.error('❌ RECONNECTION FAILED');
            if (window.showToast) {
                window.showToast('Connection failed. Please refresh the page.', 'error');
            }
        });

        socket.on('connected', function(data) {
            console.log('🌸 Server welcome message:', data);
        });

        socket.on('connect_error', function(error) {
            console.error('❌ CONNECTION ERROR:', error);
            if (window.showToast) {
                window.showToast('Connection error. Please check your internet.', 'error');
            }
        });

    } catch (error) {
        console.error('❌ Error initializing Socket.IO:', error);
        if (window.showToast) {
            window.showToast('Failed to initialize connection', 'error');
        }
    }

    // Listen for new messages from other users
    socket.on('new_message', function(data) {
        console.log('📨 NEW MESSAGE RECEIVED:', data);
        if (data.message_type === 'file') {
            console.log('📁 Received file message - file_url:', data.file_url);
            console.log('📁 Received file message - media_url:', data.media_url);
            console.log('📁 Received file message - media_type:', data.media_type);
        }
        console.log('Current contact ID:', window.currentContactId);
        console.log('Message sender ID:', data.sender_id);
        console.log('Sender ID type:', typeof data.sender_id);
        console.log('Current contact ID type:', typeof window.currentContactId);

        // Convert both to strings for comparison
        const senderIdStr = String(data.sender_id);
        const currentContactIdStr = String(window.currentContactId);

        console.log('Comparing:', senderIdStr, '==', currentContactIdStr);

        // Only show if we're currently chatting with this sender
        if (window.currentContactId && senderIdStr === currentContactIdStr) {
            console.log('✅ Message is from current contact, displaying...');
            addMessage(data.message, false, data.sender_username, data.sent_at, null, data);

            // Send delivery confirmation to sender (message was received and displayed)
            if (socket && socket.connected && data.id) {
                socket.emit('message_received', {
                    message_id: data.id,
                    sender_id: data.sender_id
                });
                console.log('📨 Sent delivery confirmation for message:', data.id);
            }

            // Auto-mark message as read since it's displayed in the current chat
            console.log('📖 Auto-marking new message as read since it\'s displayed');
            setTimeout(() => {
                markMessagesAsRead(data.sender_id);
            }, 1000); // Small delay to ensure message is rendered

            console.log('💬 Message from ' + data.sender_username + ': ' + data.message);
        } else {
            console.log('📬 Message from different contact or no contact selected');
            console.log('Not displaying in current chat');

            // Still show a notification that a message was received
            if (data.sender_username) {
                console.log('🔔 Notification: New message from ' + data.sender_username);
                showToast(`New message from ${data.sender_username}`, 'info');
            }
        }
    });

    // Listen for message sent confirmation
    socket.on('message_sent', function(data) {
        console.log('✅ Message sent confirmation:', data);
        if (data.message_type === 'file') {
            console.log('📁 File message confirmation - file_url:', data.file_url);
            console.log('📁 File message confirmation - media_url:', data.media_url);
        }

        // Update temporary message if it exists
        if (data.temp_id) {
            updateTempMessage(data.temp_id, data);
        }

        showToast('Message sent! 📤', 'success');
    });

    // Handle message delivered confirmation
    socket.on('message_delivered', function(data) {
        console.log('📨 Message delivered:', data);
        updateMessageStatus(data.message_id, data.temp_id, 'delivered');
    });

    // Handle messages read confirmation
    socket.on('messages_read', function(data) {
        console.log('📖 Messages read:', data);
        data.message_ids.forEach(messageId => {
            updateMessageStatus(messageId, null, 'read');
        });
    });

    // Listen for reaction updates
    socket.on('reaction_updated', function(data) {
        console.log('👍 Reaction updated via Socket.IO:', data);
        updateMessageReactions(data.message_id, data.reactions);
    });

    // Listen for user status changes
    socket.on('user_status_changed', function(data) {
        console.log('👤 User status changed:', data);
        console.log('🔄 Updating contact status for user', data.user_id, 'to', data.status, 'last_seen:', data.last_seen);
        updateContactStatus(data.user_id, data.status, data.last_seen);
    });

    // Listen for message errors
    socket.on('message_error', function(data) {
        console.log('❌ MESSAGE ERROR:', data);

        // Remove temporary message if it exists
        if (data.temp_id) {
            removeTempMessage(data.temp_id);
        }

        showToast('Failed to send message: ' + data.error, 'error');
    });

    socket.on('error', function(error) {
        console.error('❌ Socket.IO error:', error);
    });

    // Listen for test echo response
    socket.on('test_echo_response', function(data) {
        console.log('🧪 Test echo response received:', data);
        if (window.showToast) {
            window.showToast('Connection test successful! ✅', 'success');
        }
    });


}

// Update connection status indicator
function updateConnectionStatus(status) {
    const statusIndicator = document.querySelector('.connection-status');
    if (!statusIndicator) return;

    statusIndicator.className = 'connection-status ' + status;

    switch(status) {
        case 'connected':
            statusIndicator.innerHTML = '🟢 Connected';
            statusIndicator.title = 'Connected to server';
            break;
        case 'disconnected':
            statusIndicator.innerHTML = '🔴 Disconnected';
            statusIndicator.title = 'Disconnected from server';
            break;
        case 'connecting':
            statusIndicator.innerHTML = '🟡 Connecting...';
            statusIndicator.title = 'Connecting to server';
            break;
    }
}

// Set real status - simple and direct
function setRealStatus(status) {
    console.log('🔄 Setting REAL status to:', status);
    const myStatusElement = document.getElementById('myStatus');
    if (!myStatusElement) {
        console.log('❌ myStatus element not found');
        return;
    }

    if (status === 'online') {
        myStatusElement.innerHTML = 'Online 🟢';
        myStatusElement.style.color = '#00a884';
        console.log('✅ REAL status set to ONLINE');
    } else if (status === 'offline') {
        myStatusElement.innerHTML = 'Offline 🔴';
        myStatusElement.style.color = '#f44336';
        console.log('✅ REAL status set to OFFLINE');
    } else {
        myStatusElement.innerHTML = 'Connecting... 🔄';
        myStatusElement.style.color = '#ffc107';
        console.log('✅ REAL status set to CONNECTING');
    }
}

// Update my own status (legacy function - kept for compatibility)
function updateMyStatus(status) {
    setRealStatus(status);
}

// Format WhatsApp-style last seen time
function formatLastSeen(lastSeenStr) {
    if (!lastSeenStr) return 'last seen recently';

    const lastSeen = new Date(lastSeenStr);
    const now = new Date();
    const diffMs = now - lastSeen;
    const diffMinutes = Math.floor(diffMs / (1000 * 60));
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

    // If less than 1 minute ago, show as online
    if (diffMinutes < 1) {
        return 'online';
    }
    // If less than 60 minutes ago
    else if (diffMinutes < 60) {
        return `last seen ${diffMinutes} minute${diffMinutes === 1 ? '' : 's'} ago`;
    }
    // If today (less than 24 hours and same day)
    else if (diffHours < 24 && lastSeen.toDateString() === now.toDateString()) {
        return `last seen today at ${lastSeen.toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'})}`;
    }
    // If yesterday
    else if (diffDays === 1) {
        return `last seen yesterday at ${lastSeen.toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'})}`;
    }
    // If within a week
    else if (diffDays < 7) {
        const days = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
        return `last seen ${days[lastSeen.getDay()]} at ${lastSeen.toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'})}`;
    }
    // If more than a week ago
    else {
        return `last seen ${lastSeen.toLocaleDateString()}`;
    }
}

// Update contact status with WhatsApp-style last seen
function updateContactStatus(contactId, status, lastSeen) {
    console.log('🔄 Updating contact status for', contactId, 'to', status, 'last_seen:', lastSeen);

    let statusText = '';
    let statusColor = '#999';
    let indicatorColor = '#ddd';

    if (status === 'online') {
        statusText = 'online';
        statusColor = '#00a884';
        indicatorColor = '#00a884';
    } else {
        // Format last seen time WhatsApp style
        statusText = formatLastSeen(lastSeen);
        if (statusText === 'online') {
            statusColor = '#00a884';
            indicatorColor = '#00a884';
        } else {
            statusColor = '#999';
            indicatorColor = '#ddd';
        }
    }

    // Update contact indicator in contact list
    const indicator = document.getElementById(`indicator-${contactId}`);
    if (indicator) {
        const circle = indicator.querySelector('i');
        if (circle) {
            circle.style.color = indicatorColor;
            circle.title = statusText;
            console.log('✅ Contact indicator updated for', contactId, ':', statusText);
        }
    } else {
        console.log('❌ Contact indicator not found for', contactId);
    }

    // Update status in chat header if this is the current contact
    if (window.currentContactId && String(contactId) === String(window.currentContactId)) {
        const contactStatusElement = document.getElementById('contactStatus');
        if (contactStatusElement) {
            contactStatusElement.innerHTML = statusText;
            contactStatusElement.style.color = statusColor;
            console.log('✅ Chat header status updated for', contactId, ':', statusText);
        } else {
            console.log('❌ Contact status element not found in chat header');
        }
    }
}

// Test socket connection
function testSocketConnection() {
    console.log('🧪 Testing socket connection...');

    if (!socket) {
        console.error('❌ Socket not initialized');
        return false;
    }

    if (!socket.connected) {
        console.error('❌ Socket not connected');
        return false;
    }

    // Send test echo
    socket.emit('test_echo', {
        message: 'Connection test',
        timestamp: new Date().toISOString()
    });

    console.log('✅ Socket connection test sent');
    return true;
}

// Manual reconnect function
function reconnectSocket() {
    console.log('🔄 Manual reconnect requested');

    if (!socket) {
        console.log('🔌 No socket, initializing new connection...');
        initializeSocket();
        return;
    }

    updateConnectionStatus('connecting');

    if (socket.connected) {
        console.log('✅ Socket already connected');
        updateConnectionStatus('connected');
        if (window.showToast) {
            window.showToast('Already connected! ✅', 'info');
        }
        return;
    }

    console.log('🔄 Attempting to reconnect...');
    socket.connect();

    if (window.showToast) {
        window.showToast('Reconnecting...', 'info');
    }
}

// Debug call system
function debugCallSystem() {
    console.log('🐛 CALL SYSTEM DEBUG INFO:');
    console.log('='.repeat(50));
    console.log('Socket available:', !!window.socket);
    console.log('Socket connected:', window.socket ? window.socket.connected : 'N/A');
    console.log('Socket ID:', window.socket ? window.socket.id : 'N/A');
    console.log('WebRTC manager available:', !!window.webrtcManager);
    console.log('Current contact ID:', window.currentContactId);
    console.log('Current contact ID type:', typeof window.currentContactId);
    console.log('Current contact name:', window.currentContactName);
    console.log('Current contact color:', window.currentContactColor);
    console.log('Current contact profile picture:', window.currentContactProfilePicture);
    console.log('Current call object:', window.webrtcManager ? window.webrtcManager.currentCall : 'N/A');
    console.log('='.repeat(50));

    // Test socket connection
    if (window.socket && window.socket.connected) {
        console.log('🧪 Testing socket with echo...');
        testSocketConnection();
    }

    // Show in toast too
    if (window.showToast) {
        window.showToast('Debug info logged to console', 'info');
    }
}

// Test incoming call notification
function testIncomingCall() {
    console.log('🧪 Testing incoming call notification...');

    if (!window.webrtcManager) {
        console.error('❌ WebRTC manager not available');
        if (window.showToast) {
            window.showToast('WebRTC manager not available', 'error');
        }
        return;
    }

    // Simulate incoming call data
    const testCallData = {
        call_id: 'test_' + Date.now(),
        caller_id: 999,
        caller_username: 'Test Caller',
        caller_avatar_color: '#ff6b9d',
        caller_profile_picture: null,
        call_type: 'audio',
        timestamp: new Date().toISOString()
    };

    console.log('🧪 Simulating incoming call with data:', testCallData);

    // Manually trigger the incoming call handler
    window.webrtcManager.handleIncomingCall(testCallData);

    if (window.showToast) {
        window.showToast('Test incoming call triggered', 'info');
    }
}

// Send debug call to test notifications
function sendDebugCall() {
    if (!window.currentContactId) {
        console.error('❌ No contact selected');
        if (window.showToast) {
            window.showToast('Please select a contact first', 'error');
        }
        return;
    }

    if (!window.socket || !window.socket.connected) {
        console.error('❌ Socket not connected');
        if (window.showToast) {
            window.showToast('Socket not connected', 'error');
        }
        return;
    }

    console.log('🐛 Sending debug call to contact:', window.currentContactId);

    window.socket.emit('debug_test_call', {
        receiver_id: window.currentContactId
    });

    // Listen for confirmation
    window.socket.once('debug_test_sent', function(data) {
        console.log('🐛 Debug call sent:', data);
        if (window.showToast) {
            window.showToast(data.message, 'success');
        }
    });

    if (window.showToast) {
        window.showToast('Debug call sent!', 'info');
    }
}

// Format call duration for display
function formatCallDuration(durationSeconds) {
    if (!durationSeconds || durationSeconds <= 0) {
        return "0:00";
    }

    const duration = parseInt(durationSeconds);
    const hours = Math.floor(duration / 3600);
    const minutes = Math.floor((duration % 3600) / 60);
    const seconds = duration % 60;

    if (hours > 0) {
        return `${hours}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    } else {
        return `${minutes}:${seconds.toString().padStart(2, '0')}`;
    }
}

// Test call history messages
function testCallHistory() {
    console.log('📞 Testing WhatsApp-style call history messages...');

    // Test different types of call messages with proper data structure
    const testMessages = [
        {
            message: '📞 Missed call',
            type: 'call',
            isOwn: false,
            call_status: 'missed'
        },
        {
            message: '📞 Call • 154',
            type: 'call',
            isOwn: true,
            call_status: 'ended',
            call_duration: 154
        },
        {
            message: '📹 Call • 75',
            type: 'call',
            isOwn: false,
            call_status: 'ended',
            call_duration: 75
        },
        {
            message: '📞 Call declined',
            type: 'call',
            isOwn: true,
            call_status: 'rejected'
        },
        {
            message: '📞 Call declined',
            type: 'call',
            isOwn: false,
            call_status: 'rejected'
        }
    ];

    testMessages.forEach((testMsg, index) => {
        setTimeout(() => {
            const messageData = {
                id: `test_call_${index}`,
                message_type: testMsg.type,
                call_status: testMsg.call_status,
                call_duration: testMsg.call_duration,
                sender_username: testMsg.isOwn ? 'You' : 'Test Contact',
                sent_at: new Date().toISOString()
            };

            addMessage(testMsg.message, testMsg.isOwn, testMsg.isOwn ? null : 'Test Contact', null, null, messageData);
        }, index * 800);
    });

    if (window.showToast) {
        window.showToast('WhatsApp-style call history test added!', 'info');
    }
}

// Global call control functions (called from HTML buttons)
function acceptCall() {
    console.log('✅ Accept call button clicked');
    if (window.webrtcManager) {
        window.webrtcManager.acceptCall();
    } else {
        console.error('❌ WebRTC manager not available');
    }
}

function rejectCall() {
    console.log('❌ Reject call button clicked');
    if (window.webrtcManager) {
        window.webrtcManager.rejectCall();
    } else {
        console.error('❌ WebRTC manager not available');
    }
}

// Send message via Socket.IO
function sendMessageViaSocket(receiverId, message) {
    if (!socket || !socket.connected) {
        console.error('❌ Socket not connected, using HTTP fallback');
        console.log('Socket status:', socket ? 'exists but not connected' : 'does not exist');
        return false;
    }

    console.log('📤 SENDING MESSAGE VIA SOCKET.IO...');
    console.log('Receiver ID:', receiverId, 'Type:', typeof receiverId);
    console.log('Message:', message);
    console.log('Socket connected:', socket.connected);

    const messageData = {
        receiver_id: receiverId,
        message: message
    };

    console.log('Emitting send_message with data:', messageData);
    socket.emit('send_message', messageData);

    return true;
}

// Handle window resize to switch between desktop and mobile layouts
function handleResize() {
    const isMobile = window.innerWidth <= 768;
    const chatArea = document.getElementById('chatArea');
    const sidebar = document.querySelector('.chat-sidebar');
    const backBtn = document.getElementById('chatBackBtn');

    if (chatArea && chatArea.style.display === 'flex') {
        // Chat is currently open, adjust layout
        if (isMobile) {
            // Switch to mobile layout
            chatArea.classList.add('chat-fullscreen');
            if (sidebar) {
                sidebar.style.display = 'none';
                sidebar.style.visibility = 'hidden';
            }
            const contactsSection = document.querySelector('.contacts-section');
            if (contactsSection) {
                contactsSection.style.display = 'none';
            }
            if (backBtn) backBtn.style.display = 'flex';
        } else {
            // Switch to desktop layout
            chatArea.classList.remove('chat-fullscreen');
            if (sidebar) {
                sidebar.style.display = 'flex';
                sidebar.style.visibility = 'visible';
            }
            const contactsSection = document.querySelector('.contacts-section');
            if (contactsSection) {
                contactsSection.style.display = 'block';
            }
            if (backBtn) backBtn.style.display = 'none';
        }
    }

    // Handle virtual keyboard on mobile
    handleVirtualKeyboard();
}

// Facebook Messenger Style - Simple scroll to bottom when input focused
function handleInputFocus() {
    const messagesContainer = document.querySelector('.messages-container');
    if (messagesContainer) {
        // Simple: just scroll to bottom when input is focused
        setTimeout(() => {
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }, 300); // Delay for keyboard animation
    }
}

// Remove complex force visibility - let browser handle it naturally

// Add reaction directly (new approach)
function addDirectReaction(emoji, element) {
    console.log('🎯 Adding direct reaction:', emoji);

    let messageId = currentReactionMessageId;

    if (!messageId || messageId === 0 || messageId === null || messageId === undefined || messageId === '') {
        console.error('❌ Cannot find message ID for reaction');
        showToast('Error: Cannot find message to react to', 'error');
        return;
    }

    // Close all reaction UIs
    closeReactionBar();
    closeMoreReactions();

    // Add reaction with animation
    const messageElement = document.querySelector(`[data-message-id="${messageId}"]`);
    if (messageElement) {
        showReactionAnimation(messageElement, emoji);
    }

    console.log('📡 Sending reaction request...');

    fetch('/add_reaction', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            message_id: parseInt(messageId),
            emoji: emoji
        })
    })
    .then(response => {
        console.log('📡 Reaction response status:', response.status);
        if (!response.ok) {
            throw new Error('Network response was not ok');
        }
        return response.json();
    })
    .then(data => {
        console.log('📡 Reaction response data:', data);
        if (data.success) {
            updateMessageReactions(parseInt(messageId), data.reactions);
            showToast(`Reacted with ${emoji}!`, 'success');
        } else {
            showToast('Failed to add reaction: ' + (data.message || 'Unknown error'), 'error');
        }
    })
    .catch(error => {
        console.error('❌ Error adding reaction:', error);
        showToast('Failed to add reaction', 'error');
    });
}

// Initialize the chat system when page loads
document.addEventListener('DOMContentLoaded', function() {
    console.log('Initializing KawaiiChat...');

    // Initialize connection status
    updateConnectionStatus('connecting');

    // Initialize Socket.IO first
    initializeSocket();

    // Initialize the chat class
    if (typeof KawaiiChat !== 'undefined') {
        window.kawaiiChat = new KawaiiChat();
        console.log('✓ KawaiiChat initialized successfully');
    } else {
        console.error('❌ KawaiiChat class not found');
    }

    // Start floating emojis
    startFloatingEmojis();

    // Add resize listener
    window.addEventListener('resize', handleResize);

    // Simple Messenger-style input handling
    const messageInput = document.getElementById('messageInput');
    if (messageInput) {
        messageInput.addEventListener('focus', handleInputFocus);
    }

    // Set up real status checking
    console.log('🔄 Setting up real status monitoring...');

    // Check status every 2 seconds and update based on actual Socket.IO connection
    setInterval(function() {
        if (window.socket) {
            if (window.socket.connected) {
                setRealStatus('online');
            } else {
                setRealStatus('offline');
            }
        } else {
            setRealStatus('connecting');
        }
    }, 2000);

    // Set initial status after a short delay
    setTimeout(function() {
        console.log('🔄 Setting initial status...');
        if (window.socket && window.socket.connected) {
            setRealStatus('online');
        } else {
            setRealStatus('connecting');
        }
    }, 1000);
});

// Message Selection System
let selectedMessages = new Set();
let selectionMode = false;

// Toggle message selection
function toggleMessageSelection(messageElement, messageId) {
    if (!selectionMode) {
        enterSelectionMode();
    }

    if (selectedMessages.has(messageId)) {
        selectedMessages.delete(messageId);
        messageElement.classList.remove('message-selected');
    } else {
        selectedMessages.add(messageId);
        messageElement.classList.add('message-selected');
    }

    updateSelectionHeader();

    if (selectedMessages.size === 0) {
        exitSelectionMode();
    }
}

// Enter selection mode
function enterSelectionMode() {
    selectionMode = true;
    document.getElementById('selectionHeader').style.display = 'flex';
    document.body.style.paddingTop = '70px';

    // Make all messages selectable
    document.querySelectorAll('.message').forEach(msg => {
        msg.classList.add('message-selectable');
        msg.addEventListener('click', handleMessageClick);
    });
}

// Exit selection mode
function exitSelectionMode() {
    selectionMode = false;
    selectedMessages.clear();
    document.getElementById('selectionHeader').style.display = 'none';
    document.body.style.paddingTop = '0';

    // Remove selection styling and event listeners
    document.querySelectorAll('.message').forEach(msg => {
        msg.classList.remove('message-selectable', 'message-selected');
        msg.removeEventListener('click', handleMessageClick);
    });
}

// Handle message click in selection mode
function handleMessageClick(event) {
    if (!selectionMode) return;

    event.preventDefault();
    event.stopPropagation();

    const messageElement = event.currentTarget;
    const messageId = parseInt(messageElement.getAttribute('data-message-id'));

    if (messageId) {
        toggleMessageSelection(messageElement, messageId);
    }
}

// Update selection header
function updateSelectionHeader() {
    const count = selectedMessages.size;
    document.getElementById('selectionCount').textContent = `${count} selected`;

    // Show/hide reply button (only show if exactly 1 message selected)
    const replyBtn = document.getElementById('replyBtn');
    if (count === 1) {
        replyBtn.style.display = 'flex';
    } else {
        replyBtn.style.display = 'none';
    }
}

// Clear selection
function clearSelection() {
    exitSelectionMode();
}

// Reply to selected message
function replyToSelected() {
    if (selectedMessages.size !== 1) return;

    const messageId = Array.from(selectedMessages)[0];
    startReply(messageId);
    exitSelectionMode();
}

// Delete selected messages
function deleteSelected() {
    if (selectedMessages.size === 0) return;

    if (confirm(`Delete ${selectedMessages.size} message(s)?`)) {
        // TODO: Implement message deletion
        console.log('Delete messages:', Array.from(selectedMessages));
        exitSelectionMode();
    }
}

// Long press to start selection (mobile)
let longPressTimer = null;

function handleLongPressStart(event, messageId) {
    longPressTimer = setTimeout(() => {
        const messageElement = event.currentTarget;
        toggleMessageSelection(messageElement, messageId);
    }, 500);
}

function handleLongPressEnd() {
    if (longPressTimer) {
        clearTimeout(longPressTimer);
        longPressTimer = null;
    }
}

// Reply System
let currentReplyToMessage = null;

// Start reply to a message
function startReply(messageId) {
    // Find the message data
    const messageElement = document.querySelector(`[data-message-id="${messageId}"]`);
    if (!messageElement) return;

    // Get message data from the loaded messages or from DOM
    let messageData = null;

    // Try to find in loaded messages first
    if (window.loadedMessages) {
        messageData = window.loadedMessages.find(msg => msg.id == messageId);
    }

    // If not found, extract from DOM
    if (!messageData) {
        const messageText = messageElement.querySelector('.message-text')?.textContent || '';
        const senderElement = messageElement.querySelector('.sender-name');
        const senderName = senderElement ? senderElement.textContent : 'Unknown';
        const isImage = messageElement.querySelector('.whatsapp-image') !== null;

        messageData = {
            id: messageId,
            message: messageText,
            sender_username: senderName,
            message_type: isImage ? 'image' : 'text'
        };
    }

    currentReplyToMessage = messageData;
    showReplyPreview(messageData);

    // Focus on input
    document.getElementById('messageInput').focus();
}

// Show reply preview
function showReplyPreview(messageData) {
    const replyPreview = document.getElementById('replyPreview');
    const replyToUser = document.getElementById('replyToUser');
    const replyMessage = document.getElementById('replyMessage');

    replyToUser.textContent = messageData.sender_username || 'Unknown';

    if (messageData.message_type === 'image') {
        replyMessage.innerHTML = '<i class="fas fa-image"></i> Photo';
        replyMessage.className = 'reply-message image-reply';
    } else {
        replyMessage.textContent = messageData.message || '';
        replyMessage.className = 'reply-message';
    }

    replyPreview.style.display = 'block';
}

// Cancel reply
function cancelReply() {
    currentReplyToMessage = null;
    document.getElementById('replyPreview').style.display = 'none';
}

// Reply functionality is now integrated into the main sendMessage function

// Scroll to a specific message (for reply click)
function scrollToMessage(messageId) {
    const messageElement = document.querySelector(`[data-message-id="${messageId}"]`);
    if (messageElement) {
        messageElement.scrollIntoView({
            behavior: 'smooth',
            block: 'center'
        });

        // Highlight the message briefly
        messageElement.classList.add('message-highlight');
        setTimeout(() => {
            messageElement.classList.remove('message-highlight');
        }, 2000);
    }
}

// Chat Action Functions
async function startAudioCall() {
    console.log('🔊 Audio call button clicked');
    console.log('Current contact ID:', window.currentContactId);
    console.log('WebRTC manager available:', !!window.webrtcManager);
    console.log('Socket available:', !!window.socket);
    console.log('Socket connected:', window.socket ? window.socket.connected : 'N/A');

    if (!window.currentContactId) {
        console.error('❌ No contact selected for audio call');
        showToast('Please select a contact first', 'error');
        return;
    }

    if (!window.webrtcManager) {
        console.error('❌ WebRTC manager not available');
        showToast('Call functionality not available', 'error');
        return;
    }

    // Test socket connection first
    if (!testSocketConnection()) {
        console.error('❌ Socket connection test failed');
        showToast('Connection issue detected. Please refresh the page and try again.', 'error');
        return;
    }

    console.log('🔊 Starting audio call with contact:', window.currentContactId);
    console.log('🔊 Contact ID type:', typeof window.currentContactId);
    console.log('🔊 Contact ID value:', window.currentContactId);

    try {
        console.log('🎤 Using audio-only call method...');

        // Use the dedicated audio-only call function
        await window.webrtcManager.initiateAudioOnlyCall(window.currentContactId);

    } catch (error) {
        console.error('❌ Error starting audio call:', error);
        showToast('Failed to start audio call: ' + error.message, 'error');
    }
}

async function startVideoCall() {
    console.log('📹 Video call button clicked');
    console.log('Current contact ID:', window.currentContactId);
    console.log('WebRTC manager available:', !!window.webrtcManager);
    console.log('Socket available:', !!window.socket);
    console.log('Socket connected:', window.socket ? window.socket.connected : 'N/A');

    if (!window.currentContactId) {
        console.error('❌ No contact selected for video call');
        showToast('Please select a contact first', 'error');
        return;
    }

    if (!window.webrtcManager) {
        console.error('❌ WebRTC manager not available');
        showToast('Call functionality not available', 'error');
        return;
    }

    // Check if camera is available
    try {
        const devices = await navigator.mediaDevices.enumerateDevices();
        const cameras = devices.filter(d => d.kind === 'videoinput');

        if (cameras.length === 0) {
            console.log('📷 No camera found, will automatically start audio call');
            showToast('📷 No camera - starting audio call automatically', 'info');
        } else {
            console.log('📹 Camera found, starting video call');
        }
    } catch (e) {
        console.warn('Could not check camera availability');
    }

    console.log('📹 Starting call (video if camera available, audio if not) with contact:', window.currentContactId);

    try {
        // Run preflight check first
        console.log('🛫 Running preflight check for video...');
        const preflightResult = await window.webrtcManager.preflightCheck('video');

        if (!preflightResult.passed) {
            console.error('❌ Preflight check failed:', preflightResult.issues);
            let errorMsg = 'Cannot start video call:\n\n';
            preflightResult.issues.forEach((issue, i) => {
                errorMsg += `${i + 1}. ${issue.message}\n   Fix: ${issue.fix}\n\n`;
            });
            showToast(errorMsg, 'error');
            return;
        }

        console.log('✅ Preflight check passed, starting video call...');

        // Start the video call
        await window.webrtcManager.initiateCall(window.currentContactId, 'video');

    } catch (error) {
        console.error('❌ Error starting video call:', error);
        showToast('Failed to start call: ' + error.message, 'error');
    }
}

function showChatOptions() {
    if (!currentContact) {
        alert('Please select a contact first');
        return;
    }

    // Show options menu
    const options = [
        'View Contact Info',
        'Search Messages',
        'Mute Notifications',
        'Clear Chat',
        'Block Contact'
    ];

    const choice = prompt(`Chat Options:\n${options.map((opt, i) => `${i + 1}. ${opt}`).join('\n')}\n\nEnter option number (1-${options.length}):`);

    if (choice && choice >= 1 && choice <= options.length) {
        const selectedOption = options[choice - 1];
        alert(`Selected: ${selectedOption}`);
        console.log('⚙️ Chat option selected:', selectedOption);

        // TODO: Implement actual option functionality
    }
}

// Recent Emojis System
let recentEmojis = JSON.parse(localStorage.getItem('recentEmojis') || '["😀", "😂", "❤️", "👍", "😊", "🎉", "😍", "🔥", "✨", "💕", "🥰", "😘"]');

// Complete Emoji Collection with Animals and Flowers
const allEmojis = [
    // Faces & Emotions
    "😀", "😃", "😄", "😁", "😆", "😅", "🤣", "😂", "🙂", "🙃", "😉", "😊", "😇", "🥰", "😍", "🤩",
    "😘", "😗", "☺️", "😚", "😙", "😋", "😛", "😜", "🤪", "😝", "🤑", "🤗", "🤭", "🤫", "🤔", "🤐",
    "🤨", "😐", "😑", "😶", "😏", "😒", "🙄", "😬", "😔", "😪", "🤤", "😴", "😷", "🤒", "🤕", "🤢",
    "🤮", "🤧", "🥵", "🥶", "🥴", "😵", "🤯", "🤠", "🥳", "🥸", "😎", "🤓", "🧐", "😕", "😟", "🙁",
    "☹️", "😮", "😯", "😲", "😳", "🥺", "😦", "😧", "😨", "😰", "😥", "😢", "😭", "😱", "😖", "😣",

    // Hearts & Love
    "❤️", "🧡", "💛", "💚", "💙", "💜", "🖤", "🤍", "🤎", "💔", "❣️", "💕", "💞", "💓", "💗", "💖",
    "💘", "💝", "💟", "♥️", "💌", "💋", "💒", "👨‍❤️‍👨", "👩‍❤️‍👩", "👨‍❤️‍👩", "💑", "👪", "👨‍👩‍👧", "👨‍👩‍👦",

    // Hands & Gestures
    "👍", "👎", "👌", "✌️", "🤞", "🤟", "🤘", "🤙", "👈", "👉", "👆", "🖕", "👇", "☝️", "👏", "🙌",
    "👐", "🤲", "🤝", "🙏", "✍️", "💅", "🤳", "💪", "🦾", "🦿", "🦵", "🦶", "👂", "🦻", "👃", "🧠",

    // Animals - Complete Collection
    "🐶", "🐱", "🐭", "🐹", "🐰", "🦊", "🐻", "🐼", "🐻‍❄️", "🐨", "🐯", "🦁", "🐮", "🐷", "🐽", "🐸",
    "🐵", "🙈", "🙉", "🙊", "🐒", "🐔", "🐧", "🐦", "🐤", "🐣", "🐥", "🦆", "🦅", "🦉", "🦇", "🐺",
    "🐗", "🐴", "🦄", "🐝", "🪲", "🐛", "🦋", "🐌", "🐞", "🐜", "🪰", "🪱", "🦟", "🦗", "🕷️", "🕸️",
    "🦂", "🐢", "🐍", "🦎", "🦖", "🦕", "🐙", "🦑", "🦐", "🦞", "🦀", "🐡", "🐠", "🐟", "🐬", "🐳",
    "🐋", "🦈", "🐊", "🐅", "🐆", "🦓", "🦍", "🦧", "🦣", "🐘", "🦛", "🦏", "🐪", "🐫", "🦒", "🦘",
    "🦬", "🐃", "🐂", "🐄", "🐎", "🐖", "🐏", "🐑", "🦙", "🐐", "🦌", "🐕", "🐩", "🦮", "🐕‍🦺", "🐈",
    "🐈‍⬛", "🪶", "🐓", "🦃", "🦤", "🦚", "🦜", "🦢", "🦩", "🕊️", "🐇", "🦝", "🦨", "🦡", "🦫", "🪳",

    // Flowers & Plants - Complete Collection
    "🌸", "🌺", "🌻", "🌹", "🥀", "🌷", "🌼", "🌵", "🌲", "🌳", "🌴", "🌱", "🌿", "☘️", "🍀", "🎍",
    "🪴", "🎋", "🍃", "🍂", "🍁", "🌾", "🌽", "🌶️", "🫑", "🥒", "🥬", "🥦", "🧄", "🧅", "🍄", "🟫",
    "🌰", "🌯", "🌮", "🫔", "🌭", "🍕", "🍔", "🍟", "🥙", "🥪", "🌮", "🌯", "🫔", "🥗", "🥘", "🫕",

    // Food & Fruits
    "🍎", "🍐", "🍊", "🍋", "🍌", "🍉", "🍇", "🍓", "🫐", "🍈", "🍒", "🍑", "🥭", "🍍", "🥥", "🥝",
    "🍅", "🍆", "🥑", "🥔", "🍠", "🥐", "🥯", "🍞", "🥖", "🥨", "🧀", "🥚", "🍳", "🧈", "🥞", "🧇",

    // Sports & Activities
    "⚽", "🏀", "🏈", "⚾", "🥎", "🎾", "🏐", "🏉", "🥏", "🎱", "🪀", "🏓", "🏸", "🏒", "🏑", "🥍",
    "🏏", "🪃", "🥅", "⛳", "🪁", "🏹", "🎣", "🤿", "🥊", "🥋", "🎽", "🛹", "🛷", "⛸️", "🥌", "🎿",

    // Transportation
    "🚗", "🚕", "🚙", "🚌", "🚎", "🏎️", "🚓", "🚑", "🚒", "🚐", "🛻", "🚚", "🚛", "🚜", "🏍️", "🛵",
    "🚲", "🛴", "🛹", "🛼", "🚁", "🛸", "✈️", "🛩️", "🪂", "💺", "🚀", "🛰️", "🚉", "🚊", "🚝", "🚞",

    // Objects & Tools
    "💡", "🔦", "🕯️", "🪔", "🧯", "🛢️", "💸", "💵", "💴", "💶", "💷", "🪙", "💰", "💳", "💎", "⚖️",
    "🪜", "🧰", "🔧", "🔨", "⚒️", "🛠️", "⛏️", "🪓", "🪚", "🔩", "⚙️", "🪤", "🧲", "🔫", "💣", "🧨",

    // Celebrations & Fun
    "🎉", "🎊", "🎈", "🎁", "🎀", "🎂", "🍰", "🧁", "🍭", "🍬", "🍫", "🍿", "🍩", "🍪", "🌟", "⭐",
    "🔥", "💯", "✨", "🎯", "🎪", "🎭", "🎨", "🎬", "🎤", "🎧", "🎼", "🎵", "🎶", "🎹", "🥁", "🎷",

    // Weather & Nature
    "☀️", "🌤️", "⛅", "🌥️", "☁️", "🌦️", "🌧️", "⛈️", "🌩️", "🌨️", "❄️", "☃️", "⛄", "🌬️", "💨", "🌪️",
    "🌈", "🌊", "💧", "💦", "🫧", "🔥", "💥", "⚡", "🌟", "💫", "⭐", "🌠", "🌙", "🌛", "🌜", "🌚"
];

let emojiPickerVisible = false;

// Toggle emoji picker
function toggleEmojiPicker() {
    console.log('😊 Toggle emoji picker');
    const emojiPicker = document.getElementById('emojiPicker');

    if (!emojiPicker) {
        console.error('❌ Emoji picker not found!');
        return;
    }

    if (emojiPickerVisible) {
        emojiPicker.style.display = 'none';
        emojiPickerVisible = false;
    } else {
        emojiPicker.style.display = 'flex';
        emojiPickerVisible = true;
        loadEmojis();
    }
}

// Load emojis into picker with recent section
function loadEmojis() {
    console.log('📂 Loading emojis with recent section');

    loadRecentEmojis();
    loadAllEmojis();
}

// Load recent emojis section
function loadRecentEmojis() {
    const recentEmojiGrid = document.getElementById('recentEmojiGrid');

    if (!recentEmojiGrid) {
        console.error('❌ Recent emoji grid not found!');
        return;
    }

    // Load recent emojis
    let html = '';
    recentEmojis.forEach(emoji => {
        html += `<button class="emoji-item" onclick="insertEmoji('${emoji}')" title="${emoji}">${emoji}</button>`;
    });

    recentEmojiGrid.innerHTML = html;
    console.log('📂 Loaded', recentEmojis.length, 'recent emojis');
}

// Load all emojis section
function loadAllEmojis() {
    const allEmojiGrid = document.getElementById('allEmojiGrid');

    if (!allEmojiGrid) {
        console.error('❌ All emoji grid not found!');
        return;
    }

    // Load all emojis
    let html = '';
    allEmojis.forEach(emoji => {
        html += `<button class="emoji-item" onclick="insertEmoji('${emoji}')" title="${emoji}">${emoji}</button>`;
    });

    allEmojiGrid.innerHTML = html;
    console.log('📂 Loaded', allEmojis.length, 'all emojis');
}

// Insert emoji into message input
function insertEmoji(emoji) {
    console.log('😊 Inserting emoji:', emoji);
    const messageInput = document.getElementById('messageInput');

    if (!messageInput) {
        console.error('❌ Message input not found!');
        return;
    }

    const currentValue = messageInput.value;
    const cursorPos = messageInput.selectionStart || 0;

    // Insert emoji at cursor position
    const newValue = currentValue.slice(0, cursorPos) + emoji + currentValue.slice(cursorPos);
    messageInput.value = newValue;

    // Move cursor after the emoji
    messageInput.focus();
    messageInput.setSelectionRange(cursorPos + emoji.length, cursorPos + emoji.length);

    // Add to recent emojis
    addToRecentEmojis(emoji);

    // Close emoji picker
    closeEmojiPicker();

    console.log('😊 Emoji inserted successfully');
}

// Add emoji to recent emojis list
function addToRecentEmojis(emoji) {
    // Remove emoji if it already exists
    recentEmojis = recentEmojis.filter(e => e !== emoji);

    // Add to beginning of array
    recentEmojis.unshift(emoji);

    // Keep only last 24 emojis
    recentEmojis = recentEmojis.slice(0, 24);

    // Save to localStorage
    localStorage.setItem('recentEmojis', JSON.stringify(recentEmojis));

    console.log('📝 Added to recent emojis:', emoji);
}

// Close emoji picker
function closeEmojiPicker() {
    const emojiPicker = document.getElementById('emojiPicker');
    if (emojiPicker) {
        emojiPicker.style.display = 'none';
        emojiPickerVisible = false;
    }
}

// Initialize emoji picker
document.addEventListener('DOMContentLoaded', function() {
    console.log('😊 Initializing simple emoji picker...');

    // Close emoji picker when clicking outside
    document.addEventListener('click', function(event) {
        const emojiPicker = document.getElementById('emojiPicker');
        const emojiBtn = document.querySelector('.emoji-btn');

        if (emojiPickerVisible &&
            emojiPicker && emojiBtn &&
            !emojiPicker.contains(event.target) &&
            !emojiBtn.contains(event.target)) {
            closeEmojiPicker();
        }
    });

    console.log('😊 Emoji picker ready!');
});

// File Upload System
let selectedFile = null;

// Show file upload modal
function showFileModal() {
    console.log('📎 Opening file modal');
    const fileModal = document.getElementById('fileModal');
    if (fileModal) {
        fileModal.style.display = 'flex';
        // Reset the modal
        resetFileModal();
    }
}

// Close file upload modal
function closeFileModal() {
    console.log('📎 Closing file modal');
    const fileModal = document.getElementById('fileModal');
    if (fileModal) {
        fileModal.style.display = 'none';
        resetFileModal();
    }
}

// Reset file modal to initial state
function resetFileModal() {
    selectedFile = null;
    document.getElementById('fileUploadArea').style.display = 'block';
    document.getElementById('filePreview').style.display = 'none';
    document.getElementById('fileCaption').value = '';
    document.getElementById('sendFileBtn').disabled = true;

    // Reset file input
    const fileInput = document.getElementById('fileInput');
    if (fileInput) {
        fileInput.value = '';
    }
}

// Handle file selection
function handleFileSelect(event) {
    const file = event.target.files[0];
    if (!file) return;

    console.log('📎 File selected:', file.name, 'Size:', file.size, 'Type:', file.type);

    // Check file size (50MB limit)
    const maxSize = 50 * 1024 * 1024; // 50MB
    if (file.size > maxSize) {
        alert('File is too large! Maximum size is 50MB.');
        return;
    }

    selectedFile = file;
    showFilePreview(file);
}

// Show file preview
function showFilePreview(file) {
    const fileUploadArea = document.getElementById('fileUploadArea');
    const filePreview = document.getElementById('filePreview');
    const fileInfo = document.getElementById('fileInfo');
    const sendFileBtn = document.getElementById('sendFileBtn');

    // Hide upload area and show preview
    fileUploadArea.style.display = 'none';
    filePreview.style.display = 'block';
    sendFileBtn.disabled = false;

    // Format file size
    const fileSize = formatFileSize(file.size);
    const fileType = getFileTypeIcon(file.type);

    // Create file preview HTML
    fileInfo.innerHTML = `
        <div class="file-preview-item">
            <div class="file-icon">
                <i class="${fileType.icon}" style="color: ${fileType.color}"></i>
            </div>
            <div class="file-details">
                <div class="file-name">${file.name}</div>
                <div class="file-meta">${fileType.name} • ${fileSize}</div>
            </div>
            <button class="remove-file-btn" onclick="removeSelectedFile()">
                <i class="fas fa-times"></i>
            </button>
        </div>
    `;
}

// Remove selected file
function removeSelectedFile() {
    resetFileModal();
}

// Format file size
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// Get file type icon and color
function getFileTypeIcon(mimeType) {
    if (mimeType.startsWith('video/')) {
        return { icon: 'fas fa-video', color: '#ff6b6b', name: 'Video' };
    } else if (mimeType.startsWith('audio/')) {
        return { icon: 'fas fa-music', color: '#4ecdc4', name: 'Audio' };
    } else if (mimeType.startsWith('image/')) {
        return { icon: 'fas fa-image', color: '#45b7d1', name: 'Image' };
    } else if (mimeType.includes('pdf')) {
        return { icon: 'fas fa-file-pdf', color: '#e74c3c', name: 'PDF' };
    } else if (mimeType.includes('word') || mimeType.includes('document')) {
        return { icon: 'fas fa-file-word', color: '#2980b9', name: 'Document' };
    } else if (mimeType.includes('sheet') || mimeType.includes('excel')) {
        return { icon: 'fas fa-file-excel', color: '#27ae60', name: 'Spreadsheet' };
    } else if (mimeType.includes('zip') || mimeType.includes('rar') || mimeType.includes('archive')) {
        return { icon: 'fas fa-file-archive', color: '#f39c12', name: 'Archive' };
    } else {
        return { icon: 'fas fa-file', color: '#95a5a6', name: 'File' };
    }
}

// Send file function
async function sendFile() {
    if (!selectedFile) {
        alert('Please select a file first!');
        return;
    }

    if (!window.currentContactId) {
        alert('Please select a contact first!');
        return;
    }

    const caption = document.getElementById('fileCaption').value.trim();
    const sendFileBtn = document.getElementById('sendFileBtn');

    console.log('📎 Sending file:', selectedFile.name);

    // Disable send button and show loading
    sendFileBtn.disabled = true;
    sendFileBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Uploading...';

    try {
        // Upload file directly via backend endpoint
        const formData = new FormData();
        formData.append('file', selectedFile);
        formData.append('receiver_id', window.currentContactId);
        formData.append('caption', caption || '');

        console.log('📤 Uploading file via backend:', selectedFile.name);

        const response = await fetch('/upload_media', {
            method: 'POST',
            body: formData
        });

        if (!response.ok) {
            const errorText = await response.text();
            console.error('❌ Upload failed:', errorText);
            throw new Error(`Upload failed: ${response.status}`);
        }

        const result = await response.json();

        if (!result.success) {
            throw new Error(result.message || 'Upload failed');
        }

        console.log('✅ File uploaded successfully:', result.message);

        // Add the message to chat display for the sender
        if (result.message) {
            addMessage(result.message.message || caption || '', true, null, null, null, result.message);
        }

        // Close modal and show success
        closeFileModal();
        showToast('File sent successfully! 📎', 'success');

    } catch (error) {
        console.error('❌ Error sending file:', error);
        showToast('Failed to send file: ' + error.message, 'error');

        // Re-enable send button
        sendFileBtn.disabled = false;
        sendFileBtn.innerHTML = '<i class="fas fa-paper-plane"></i> Send';
    }
}

// Upload file to Cloudinary using backend endpoint
async function uploadFileToCloudinary(file) {
    console.log('☁️ Uploading to Cloudinary via backend:', file.name, 'Type:', file.type, 'Size:', file.size);

    try {
        // Use the existing backend upload endpoint
        const formData = new FormData();
        formData.append('file', file);

        // Determine which backend endpoint to use
        let uploadEndpoint = '/upload_media';
        if (file.type.startsWith('image/')) {
            uploadEndpoint = '/upload_image';
        }

        console.log(`🚀 Uploading via backend endpoint: ${uploadEndpoint}`);

        const response = await fetch(uploadEndpoint, {
            method: 'POST',
            body: formData
        });

        console.log('📡 Backend response status:', response.status);

        if (!response.ok) {
            const errorText = await response.text();
            console.error('❌ Backend upload error:', errorText);
            throw new Error(`Backend upload failed: ${response.status}`);
        }

        const result = await response.json();

        if (result.success && result.file_url) {
            console.log('✅ Backend upload successful!');
            console.log('📄 Upload result:', {
                url: result.file_url,
                message: result.message
            });
            return result.file_url;
        } else {
            console.error('❌ Backend upload failed:', result.message);
            throw new Error(result.message || 'Upload failed');
        }

    } catch (error) {
        console.error('❌ Backend upload error:', error);
        throw error; // Re-throw the error instead of using fallbacks
    }
}

// Download file function
function downloadFile(fileUrl, fileName) {
    console.log('📥 Downloading file:', fileName);

    // Create a temporary link element and trigger download
    const link = document.createElement('a');
    link.href = fileUrl;
    link.download = fileName;
    link.target = '_blank';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    showToast('Downloading ' + fileName + '...', 'info');
}

// Video Player Functions
let currentVideoUrl = '';
let currentVideoName = '';

// Open video player modal
function openVideoPlayer(videoUrl, videoName, senderName, sentAt) {
    console.log('🎬 Opening video player:', videoName, 'URL:', videoUrl);

    // Check if URL is valid
    if (!videoUrl) {
        console.log('🎬 No video URL provided');
        showVideoDownloadOnly(videoName, senderName, sentAt);
        return;
    }

    currentVideoUrl = videoUrl;
    currentVideoName = videoName;

    const modal = document.getElementById('videoPlayerModal');
    const video = document.getElementById('videoPlayerElement');
    const source = document.getElementById('videoPlayerSource');
    const title = document.getElementById('videoPlayerTitle');
    const meta = document.getElementById('videoPlayerMeta');
    const errorMessage = document.getElementById('videoErrorMessage');
    const loadingMessage = document.getElementById('videoLoadingMessage');

    // Reset video element
    video.pause();
    video.currentTime = 0;

    // Hide error and show loading
    errorMessage.style.display = 'none';
    loadingMessage.style.display = 'flex';
    video.style.display = 'none';

    // Detect MIME type from URL or filename
    const mimeType = detectVideoMimeType(videoUrl, videoName);
    console.log('🎬 Detected MIME type:', mimeType);

    // Set video source with proper MIME type
    source.src = videoUrl;
    source.type = mimeType;

    // Set title and meta info
    title.textContent = videoName;
    meta.textContent = `Sent by ${senderName}${sentAt ? ' • ' + formatTime(sentAt) : ''}`;

    // Show modal
    modal.style.display = 'flex';
    document.body.style.overflow = 'hidden'; // Prevent background scrolling

    // Add event listeners for better debugging and control
    video.addEventListener('loadstart', () => console.log('🎬 Load start'));
    video.addEventListener('loadeddata', () => console.log('🎬 Loaded data'));
    video.addEventListener('canplay', () => console.log('🎬 Can play'));
    video.addEventListener('canplaythrough', () => console.log('🎬 Can play through'));
    video.addEventListener('error', (e) => console.error('🎬 Video error:', e));
    video.addEventListener('play', () => console.log('🎬 Video started playing'));
    video.addEventListener('pause', () => console.log('🎬 Video paused'));
    video.addEventListener('ended', () => console.log('🎬 Video ended'));

    // Add cleanup when video ends
    video.addEventListener('ended', () => {
        console.log('🎬 Video playback completed');
        video.currentTime = 0; // Reset to beginning
    });

    // Load video
    console.log('🎬 Loading video...');
    video.load();

    // Test if URL is accessible first
    testVideoUrl(videoUrl).then(isAccessible => {
        if (!isAccessible) {
            console.log('🎬 Video URL not accessible');
            handleVideoError();
            return;
        }

        // Set a timeout to check if video loads
        setTimeout(() => {
            if (video.readyState === 0) {
                console.log('🎬 Video not loading, trying alternative approach');
                tryDirectVideoLoad(videoUrl, mimeType);
            }
        }, 3000);
    });
}

// Close video player modal
function closeVideoPlayer() {
    console.log('🎬 Closing video player');

    const modal = document.getElementById('videoPlayerModal');
    const video = document.getElementById('videoPlayerElement');

    // Completely stop the video
    if (video) {
        video.pause();
        video.currentTime = 0;
        video.src = ''; // Clear the video source
        video.load(); // Reload the video element to stop any background loading
        console.log('🎬 Video stopped and source cleared');
    }

    // Hide modal
    if (modal) {
        modal.style.display = 'none';
    }
    document.body.style.overflow = 'auto'; // Restore scrolling

    // Clear video data
    currentVideoUrl = '';
    currentVideoName = '';

    // Hide any error or loading messages
    const errorMessage = document.getElementById('videoErrorMessage');
    const loadingMessage = document.getElementById('videoLoadingMessage');
    if (errorMessage) errorMessage.style.display = 'none';
    if (loadingMessage) loadingMessage.style.display = 'none';
}

// Download current video
function downloadCurrentVideo() {
    if (currentVideoUrl && currentVideoName) {
        console.log('📥 Downloading video:', currentVideoName);
        const link = document.createElement('a');
        link.href = currentVideoUrl;
        link.download = currentVideoName;
        link.target = '_blank';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    }
}





// Toggle video play/pause
function toggleVideoPlayPause() {
    const video = document.getElementById('videoPlayerElement');

    if (video.paused) {
        video.play();
    } else {
        video.pause();
    }
}

// Download current video
function downloadCurrentVideo() {
    if (currentVideoUrl && currentVideoName) {
        downloadFile(currentVideoUrl, currentVideoName);
    }
}

// Toggle fullscreen
function toggleVideoFullscreen() {
    const video = document.getElementById('videoPlayerElement');

    if (!document.fullscreenElement) {
        video.requestFullscreen().catch(err => {
            console.error('Error attempting to enable fullscreen:', err);
        });
    } else {
        document.exitFullscreen();
    }
}

// Format time for video player
function formatTime(timestamp) {
    if (!timestamp) return '';

    const date = new Date(timestamp);
    const now = new Date();
    const diffTime = Math.abs(now - date);
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays === 1) {
        return 'Today ' + date.toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});
    } else if (diffDays === 2) {
        return 'Yesterday ' + date.toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});
    } else if (diffDays <= 7) {
        return date.toLocaleDateString([], {weekday: 'short'}) + ' ' + date.toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});
    } else {
        return date.toLocaleDateString() + ' ' + date.toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});
    }
}

// Detect video MIME type from URL or filename
function detectVideoMimeType(url, filename) {
    // First try to get extension from filename
    let extension = '';
    if (filename) {
        const parts = filename.toLowerCase().split('.');
        extension = parts[parts.length - 1];
    }

    // If no extension from filename, try URL
    if (!extension && url) {
        const urlParts = url.toLowerCase().split('.');
        const lastPart = urlParts[urlParts.length - 1];
        // Remove query parameters
        extension = lastPart.split('?')[0];
    }

    console.log('🎬 File extension detected:', extension);

    // Map extensions to MIME types
    const mimeTypes = {
        'mp4': 'video/mp4',
        'webm': 'video/webm',
        'ogg': 'video/ogg',
        'avi': 'video/avi',
        'mov': 'video/quicktime',
        'wmv': 'video/x-ms-wmv',
        'flv': 'video/x-flv',
        'mkv': 'video/x-matroska',
        '3gp': 'video/3gpp',
        'm4v': 'video/mp4'
    };

    return mimeTypes[extension] || 'video/mp4'; // Default to mp4
}

// Handle video loading start
function handleVideoLoadStart() {
    console.log('🎬 Video loading started');
    const loadingMessage = document.getElementById('videoLoadingMessage');
    const errorMessage = document.getElementById('videoErrorMessage');
    const video = document.getElementById('videoPlayerElement');

    loadingMessage.style.display = 'flex';
    errorMessage.style.display = 'none';
    video.style.display = 'none';

    // Set a timeout to show error if loading takes too long
    setTimeout(() => {
        if (loadingMessage.style.display === 'flex') {
            console.log('🎬 Video loading timeout, showing error');
            handleVideoError();
        }
    }, 10000); // 10 second timeout
}

// Handle video can play
function handleVideoCanPlay() {
    console.log('🎬 Video can play');
    const loadingMessage = document.getElementById('videoLoadingMessage');
    const errorMessage = document.getElementById('videoErrorMessage');
    const video = document.getElementById('videoPlayerElement');

    loadingMessage.style.display = 'none';
    errorMessage.style.display = 'none';
    video.style.display = 'block';

    // Try to play the video
    video.play().catch(e => {
        console.log('🎬 Auto-play failed:', e);
    });
}

// Handle video error
function handleVideoError() {
    console.error('🎬 Video error occurred');
    const video = document.getElementById('videoPlayerElement');
    const loadingMessage = document.getElementById('videoLoadingMessage');
    const errorMessage = document.getElementById('videoErrorMessage');

    loadingMessage.style.display = 'none';
    video.style.display = 'none';
    errorMessage.style.display = 'flex';

    // Try alternative video formats
    tryAlternativeVideoFormats();
}

// Try alternative video formats
function tryAlternativeVideoFormats() {
    if (!currentVideoUrl) return;

    console.log('🎬 Trying alternative video formats for URL:', currentVideoUrl);

    // Check if it's a placeholder URL (from our fallback system)
    if (currentVideoUrl.includes('via.placeholder.com')) {
        console.log('🎬 Detected placeholder URL, showing download option only');
        return; // Don't try to play placeholder images as videos
    }

    const video = document.getElementById('videoPlayerElement');
    const source = document.getElementById('videoPlayerSource');

    // Try different MIME types
    const alternativeTypes = [
        'video/mp4',
        'video/webm',
        'video/ogg',
        '',  // Let browser auto-detect
        'application/octet-stream'  // Generic binary
    ];

    let currentTypeIndex = 0;

    function tryNextType() {
        if (currentTypeIndex < alternativeTypes.length) {
            const mimeType = alternativeTypes[currentTypeIndex];
            console.log('🎬 Trying MIME type:', mimeType || 'auto-detect');

            source.type = mimeType;
            video.load();

            currentTypeIndex++;

            // If this fails, try the next one
            video.onerror = () => {
                setTimeout(tryNextType, 1000);
            };
        } else {
            console.error('🎬 All video formats failed');
        }
    }

    tryNextType();
}

// Handle video thumbnail error
function handleVideoThumbnailError(videoElement) {
    console.log('🎬 Video thumbnail failed to load, showing fallback');

    // Hide the video element and show a fallback
    videoElement.style.display = 'none';

    // Create fallback content
    const thumbnail = videoElement.closest('.video-thumbnail');
    if (thumbnail) {
        const fallback = document.createElement('div');
        fallback.className = 'video-thumbnail-fallback';
        fallback.innerHTML = `
            <div class="video-fallback-icon">
                <i class="fas fa-video"></i>
            </div>
            <div class="video-fallback-text">Video</div>
        `;
        thumbnail.appendChild(fallback);
    }
}

// Update video duration in thumbnail
function updateVideoDuration(videoElement, messageId) {
    const duration = videoElement.duration;
    if (duration && !isNaN(duration)) {
        const durationElement = document.getElementById(`duration-${messageId}`);
        if (durationElement) {
            const minutes = Math.floor(duration / 60);
            const seconds = Math.floor(duration % 60);
            const formattedDuration = `${minutes}:${seconds.toString().padStart(2, '0')}`;
            durationElement.innerHTML = `<i class="fas fa-video"></i> ${formattedDuration}`;
        }
    }
}

// Try direct video loading as fallback
function tryDirectVideoLoad(videoUrl, mimeType) {
    console.log('🎬 Trying direct video load');
    const video = document.getElementById('videoPlayerElement');

    // Try setting src directly on video element
    video.src = videoUrl;
    video.load();

    // If still not working after 5 seconds, show error
    setTimeout(() => {
        if (video.readyState === 0) {
            console.log('🎬 Direct load failed, showing error');
            handleVideoError();
        }
    }, 5000);
}

// Show video download only (for invalid URLs)
function showVideoDownloadOnly(videoName, senderName, sentAt) {
    const modal = document.getElementById('videoPlayerModal');
    const video = document.getElementById('videoPlayerElement');
    const title = document.getElementById('videoPlayerTitle');
    const meta = document.getElementById('videoPlayerMeta');
    const errorMessage = document.getElementById('videoErrorMessage');
    const loadingMessage = document.getElementById('videoLoadingMessage');

    // Hide video and loading, show error
    video.style.display = 'none';
    loadingMessage.style.display = 'none';
    errorMessage.style.display = 'flex';

    // Set title and meta info
    title.textContent = videoName;
    meta.textContent = `Sent by ${senderName}${sentAt ? ' • ' + formatTime(sentAt) : ''}`;

    // Show modal
    modal.style.display = 'flex';
    document.body.style.overflow = 'hidden';
}

// Test video URL accessibility
async function testVideoUrl(url) {
    try {
        const response = await fetch(url, { method: 'HEAD' });
        return response.ok;
    } catch (error) {
        console.error('🎬 URL test failed:', error);
        return false;
    }
}

// Close video player when pressing Escape key
document.addEventListener('keydown', function(event) {
    if (event.key === 'Escape') {
        const modal = document.getElementById('videoPlayerModal');
        if (modal && modal.style.display === 'flex') {
            closeVideoPlayer();
        }
    }
});

// Ensure video stops when page is unloaded or hidden
document.addEventListener('visibilitychange', function() {
    if (document.hidden) {
        const modal = document.getElementById('videoPlayerModal');
        if (modal && modal.style.display === 'flex') {
            const video = document.getElementById('videoPlayerElement');
            if (video && !video.paused) {
                video.pause();
                console.log('🎬 Video paused due to page visibility change');
            }
        }
    }
});

// Stop video when page is about to unload
window.addEventListener('beforeunload', function() {
    const video = document.getElementById('videoPlayerElement');
    if (video && !video.paused) {
        video.pause();
        video.src = '';
        console.log('🎬 Video stopped before page unload');
    }
});













</script>
{% endblock %}
